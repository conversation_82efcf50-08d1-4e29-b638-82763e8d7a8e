/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_da_js"],{

/***/ "./node_modules/dayjs/locale/da.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/da.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var d=t(e),a={name:\"da\",weekdays:\"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag\".split(\"_\"),weekdaysShort:\"søn._man._tirs._ons._tors._fre._lør.\".split(\"_\"),weekdaysMin:\"sø._ma._ti._on._to._fr._lø.\".split(\"_\"),months:\"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan._feb._mar._apr._maj_juni_juli_aug._sept._okt._nov._dec.\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){return e+\".\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY HH:mm\",LLLL:\"dddd [d.] D. MMMM YYYY [kl.] HH:mm\"},relativeTime:{future:\"om %s\",past:\"%s siden\",s:\"få sekunder\",m:\"et minut\",mm:\"%d minutter\",h:\"en time\",hh:\"%d timer\",d:\"en dag\",dd:\"%d dage\",M:\"en måned\",MM:\"%d måneder\",y:\"et år\",yy:\"%d år\"}};return d.default.locale(a,null,!0),a}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/da.js\n"));

/***/ })

}]);