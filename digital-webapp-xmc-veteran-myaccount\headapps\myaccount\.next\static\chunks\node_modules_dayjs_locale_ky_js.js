/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ky_js"],{

/***/ "./node_modules/dayjs/locale/ky.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ky.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ky\",weekdays:\"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби\".split(\"_\"),months:\"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь\".split(\"_\"),weekStart:1,weekdaysShort:\"Жек_Дүй_Шей_Шар_Бей_Жум_Ише\".split(\"_\"),monthsShort:\"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек\".split(\"_\"),weekdaysMin:\"Жк_Дй_Шй_Шр_Бй_Жм_Иш\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"%s ичинде\",past:\"%s мурун\",s:\"бирнече секунд\",m:\"бир мүнөт\",mm:\"%d мүнөт\",h:\"бир саат\",hh:\"%d саат\",d:\"бир күн\",dd:\"%d күн\",M:\"бир ай\",MM:\"%d ай\",y:\"бир жыл\",yy:\"%d жыл\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ky.js\n"));

/***/ })

}]);