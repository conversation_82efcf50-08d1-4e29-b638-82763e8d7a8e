/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_my_js"],{

/***/ "./node_modules/dayjs/locale/my.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/my.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"my\",weekdays:\"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ\".split(\"_\"),months:\"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ\".split(\"_\"),weekStart:1,weekdaysShort:\"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ\".split(\"_\"),monthsShort:\"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ\".split(\"_\"),weekdaysMin:\"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"လာမည့် %s မှာ\",past:\"လွန်ခဲ့သော %s က\",s:\"စက္ကန်.အနည်းငယ်\",m:\"တစ်မိနစ်\",mm:\"%d မိနစ်\",h:\"တစ်နာရီ\",hh:\"%d နာရီ\",d:\"တစ်ရက်\",dd:\"%d ရက်\",M:\"တစ်လ\",MM:\"%d လ\",y:\"တစ်နှစ်\",yy:\"%d နှစ်\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/my.js\n"));

/***/ })

}]);