/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_fy_js"],{

/***/ "./node_modules/dayjs/locale/fy.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/fy.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,n){ true?module.exports=n(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=n(e),t={name:\"fy\",weekdays:\"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon\".split(\"_\"),months:\"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber\".split(\"_\"),monthsShort:\"jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.\".split(\"_\"),weekStart:1,weekdaysShort:\"si._mo._ti._wo._to._fr._so.\".split(\"_\"),weekdaysMin:\"Si_Mo_Ti_Wo_To_Fr_So\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD-MM-YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"oer %s\",past:\"%s lyn\",s:\"in pear sekonden\",m:\"ien minút\",mm:\"%d minuten\",h:\"ien oere\",hh:\"%d oeren\",d:\"ien dei\",dd:\"%d dagen\",M:\"ien moanne\",MM:\"%d moannen\",y:\"ien jier\",yy:\"%d jierren\"}};return i.default.locale(t,null,!0),t}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/fy.js\n"));

/***/ })

}]);