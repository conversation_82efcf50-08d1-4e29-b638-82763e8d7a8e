/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_af_js"],{

/***/ "./node_modules/dayjs/locale/af.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/af.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,a){ true?module.exports=a(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),t={name:\"af\",weekdays:\"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag\".split(\"_\"),months:\"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember\".split(\"_\"),weekStart:1,weekdaysShort:\"Son_Maa_Din_Woe_Don_Vry_Sat\".split(\"_\"),monthsShort:\"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des\".split(\"_\"),weekdaysMin:\"So_Ma_Di_Wo_Do_Vr_Sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"oor %s\",past:\"%s gelede\",s:\"'n paar sekondes\",m:\"'n minuut\",mm:\"%d minute\",h:\"'n uur\",hh:\"%d ure\",d:\"'n dag\",dd:\"%d dae\",M:\"'n maand\",MM:\"%d maande\",y:\"'n jaar\",yy:\"%d jaar\"}};return n.default.locale(t,null,!0),t}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/af.js\n"));

/***/ })

}]);