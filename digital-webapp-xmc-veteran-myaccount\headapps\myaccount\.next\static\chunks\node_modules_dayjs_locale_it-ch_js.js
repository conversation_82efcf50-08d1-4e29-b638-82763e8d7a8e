/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_it-ch_js"],{

/***/ "./node_modules/dayjs/locale/it-ch.js":
/*!********************************************!*\
  !*** ./node_modules/dayjs/locale/it-ch.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,o){ true?module.exports=o(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=o(e),t={name:\"it-ch\",weekdays:\"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato\".split(\"_\"),months:\"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre\".split(\"_\"),weekStart:1,weekdaysShort:\"dom_lun_mar_mer_gio_ven_sab\".split(\"_\"),monthsShort:\"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic\".split(\"_\"),weekdaysMin:\"do_lu_ma_me_gi_ve_sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"tra %s\",past:\"%s fa\",s:\"alcuni secondi\",m:\"un minuto\",mm:\"%d minuti\",h:\"un'ora\",hh:\"%d ore\",d:\"un giorno\",dd:\"%d giorni\",M:\"un mese\",MM:\"%d mesi\",y:\"un anno\",yy:\"%d anni\"}};return n.default.locale(t,null,!0),t}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/it-ch.js\n"));

/***/ })

}]);