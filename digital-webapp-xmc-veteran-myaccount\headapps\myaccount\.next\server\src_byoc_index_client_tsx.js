"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_byoc_index_client_tsx";
exports.ids = ["src_byoc_index_client_tsx"];
exports.modules = {

/***/ "./src/byoc/index.client.tsx":
/*!***********************************!*\
  !*** ./src/byoc/index.client.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-feaas/clientside/react */ \"@sitecore-feaas/clientside/react\");\n/* harmony import */ var _sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @sitecore/components/form */ \"@sitecore/components/form\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__]);\n_sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n/**\r\n * Below are Sitecore default BYOC components. Included components will be available in Pages and Components apps out of the\r\n * box for convenience. It is advised to comment out unused components when application is ready for production\r\n * to reduce javascript bundle size.\r\n */ // SitecoreForm component displays forms created in XM Forms as individual components to be embedded into Pages.\n// Sitecore Forms for Sitecore XP are still available separately via @sitecore-jss-forms package\n\n/**\r\n * End of built-in JSS imports\r\n * You can import your own client component below\r\n * @example\r\n * import './MyClientComponent';\r\n * @example\r\n * import 'src/otherFolder/MyOtherComponent';\r\n */ // An important boilerplate component that prevents BYOC components from being optimized away and allows then. Should be kept in this file.\nconst ClientsideComponent = (props)=>_sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__.ExternalComponent(props);\n/**\r\n * Clientside BYOC component will be rendered in the browser, so that external components:\r\n * - Can have access to DOM apis, including network requests\r\n * - Use clientside react hooks like useEffect.\r\n * - Be implemented as web components.\r\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientsideComponent);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/byoc/index.client.tsx\n");

/***/ })

};
;