import { faUser, faChevronUp, faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { UnstyledButton } from '@mantine/core';
import { Text, Field, withDatasourceCheck } from '@sitecore-jss/sitecore-jss-nextjs';
import { getCookie } from 'cookies-next';
import { ComponentProps } from 'lib/component-props';
import { useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type AvatarProps = ComponentProps & {
  fields: {
    WelcomeText: Field<string>;
    MyAccountText: Field<string>;
  };
};

const Avatar = (props: AvatarProps): JSX.Element | null => {
  const [showMenu, setShowMenu] = useState(false);
  const custFName = getCookie('customer_name');
  const [firstName] = useState(custFName !== undefined ? custFName : '');
  const racfId = getCookie('racf_Id');
  const [racfIds] = useState(racfId !== undefined ? racfId : '');


  console.log( props.fields.WelcomeText.value, 'WelcomeText');

  return (
    <div className="ml-auto">
      <div className="hidden md:block wide:hidden ipad:hidden ml-6">
        {/* avatar */}
        <UnstyledButton
          className="text-textPrimary text-base font-primaryBlack flex flex-row items-center my-4"
          onClick={() => setShowMenu(!showMenu)}
          aria-label="Click to access user settings"
        >
           <div className="text-minus1 text-white font-primaryBold rounded-full flex items-center justify-center bg-bgPrimary w-[40px] h-[40px]">
              {typeof firstName === 'string' && firstName.length > 0 ? (
                <span className="text-minus1">{firstName[0].toUpperCase()}</span>
              ) : (
                <span className="text-minus1">U</span>
              )}
            </div>
          <div className="pl-[10px] flex flex-row mt-1">
            <Text
              tag="p"
              className="text-base leading-[20px] font-primaryBold text-textUndenary normal-case"
              field={{
                value:
                  props.fields.WelcomeText.value +
                  (firstName ? firstName : 'user') +
                  (props.fields.WelcomeText.value ? '!' : ''),
              }}
            />
             <p className="text-base pl-[10px]  leading-[20px] font-primaryBold text-textDenary ml-2">
                {racfIds ? racfIds : ''}
              </p>
            <div className="flex items-center md:hidden">
              <Text
                tag="p"
                className="text-textSecondary font-primaryRegular text-base leading-[22px]"
                field={{ value: props.fields.MyAccountText?.value }}
              />
              {showMenu ? (
                <FontAwesomeIcon
                  className="pl-[4px] text-textPrimary h-[15px] w-[15px] border-solid"
                  icon={faChevronUp}
                />
              ) : (
                <FontAwesomeIcon
                  className="pl-[4px] text-textPrimary h-[15px] w-[15px] border-solid"
                  icon={faChevronDown}
                />
              )}
            </div>
          </div>
        </UnstyledButton>
      </div>
    </div>
  );
};
export { Avatar };
const Component = withDatasourceCheck()<AvatarProps>(Avatar);
export default aiLogger(Component, Component.name);
