/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_pt_js"],{

/***/ "./node_modules/dayjs/locale/pt.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/pt.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,a){ true?module.exports=a(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var o=a(e),t={name:\"pt\",weekdays:\"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado\".split(\"_\"),weekdaysShort:\"dom_seg_ter_qua_qui_sex_sab\".split(\"_\"),weekdaysMin:\"Do_2ª_3ª_4ª_5ª_6ª_Sa\".split(\"_\"),months:\"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro\".split(\"_\"),monthsShort:\"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez\".split(\"_\"),ordinal:function(e){return e+\"º\"},weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY [às] HH:mm\",LLLL:\"dddd, D [de] MMMM [de] YYYY [às] HH:mm\"},relativeTime:{future:\"em %s\",past:\"há %s\",s:\"alguns segundos\",m:\"um minuto\",mm:\"%d minutos\",h:\"uma hora\",hh:\"%d horas\",d:\"um dia\",dd:\"%d dias\",M:\"um mês\",MM:\"%d meses\",y:\"um ano\",yy:\"%d anos\"}};return o.default.locale(t,null,!0),t}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/pt.js\n"));

/***/ })

}]);