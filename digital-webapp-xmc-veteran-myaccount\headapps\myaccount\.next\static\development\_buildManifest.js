self.__BUILD_MANIFEST = (function(a){return {__rewrites:{afterFiles:[{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Fsitecore\u002Fapi\u002F:path*",destination:"\u002F:nextInternalLocale\u002Fsitecore\u002Fapi\u002F:path*"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002F-\u002F:path*",destination:"\u002F:nextInternalLocale\u002F-\u002F:path*"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Fhealthz",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fhealthz"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Fsitecore\u002Fservice\u002F:path*",destination:"\u002F:nextInternalLocale\u002Fsitecore\u002Fservice\u002F:path*"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Flayouts\u002Fsystem\u002F:path*",destination:"\u002F:nextInternalLocale\u002Flayouts\u002Fsystem\u002F:path*"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Ffeaas-render",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fediting\u002Ffeaas\u002Frender"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Frobots.txt",destination:"\u002F:nextInternalLocale\u002Fapi\u002Frobots"},{has:a,source:"\u002F:nextInternalLocale(en|es)\u002Fsitemap:id([\\w-]{0,}).xml",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fsitemap"}],beforeFiles:[],fallback:[]},"/404":["static\u002Fchunks\u002Fpages\u002F404.js"],"/_error":["static\u002Fchunks\u002Fpages\u002F_error.js"],"/[[...path]]":["static\u002Fchunks\u002Fpages\u002F[[...path]].js"],sortedPages:["\u002F404","\u002F_app","\u002F_error","\u002F[[...path]]"]}}(void 0));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()