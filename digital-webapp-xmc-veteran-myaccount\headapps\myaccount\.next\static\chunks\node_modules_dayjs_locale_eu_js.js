/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_eu_js"],{

/***/ "./node_modules/dayjs/locale/eu.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/eu.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(a,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var t=e(a),l={name:\"eu\",weekdays:\"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata\".split(\"_\"),months:\"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua\".split(\"_\"),weekStart:1,weekdaysShort:\"ig._al._ar._az._og._ol._lr.\".split(\"_\"),monthsShort:\"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.\".split(\"_\"),weekdaysMin:\"ig_al_ar_az_og_ol_lr\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY[ko] MMMM[ren] D[a]\",LLL:\"YYYY[ko] MMMM[ren] D[a] HH:mm\",LLLL:\"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm\",l:\"YYYY-M-D\",ll:\"YYYY[ko] MMM D[a]\",lll:\"YYYY[ko] MMM D[a] HH:mm\",llll:\"ddd, YYYY[ko] MMM D[a] HH:mm\"},relativeTime:{future:\"%s barru\",past:\"duela %s\",s:\"segundo batzuk\",m:\"minutu bat\",mm:\"%d minutu\",h:\"ordu bat\",hh:\"%d ordu\",d:\"egun bat\",dd:\"%d egun\",M:\"hilabete bat\",MM:\"%d hilabete\",y:\"urte bat\",yy:\"%d urte\"}};return t.default.locale(l,null,!0),l}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/eu.js\n"));

/***/ })

}]);