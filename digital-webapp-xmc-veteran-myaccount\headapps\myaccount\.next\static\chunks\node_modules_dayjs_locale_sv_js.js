/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_sv_js"],{

/***/ "./node_modules/dayjs/locale/sv.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/sv.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=t(e),d={name:\"sv\",weekdays:\"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag\".split(\"_\"),weekdaysShort:\"sön_mån_tis_ons_tor_fre_lör\".split(\"_\"),weekdaysMin:\"sö_må_ti_on_to_fr_lö\".split(\"_\"),months:\"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){var t=e%10;return\"[\"+e+(1===t||2===t?\"a\":\"e\")+\"]\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY [kl.] HH:mm\",LLLL:\"dddd D MMMM YYYY [kl.] HH:mm\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd D MMM YYYY HH:mm\"},relativeTime:{future:\"om %s\",past:\"för %s sedan\",s:\"några sekunder\",m:\"en minut\",mm:\"%d minuter\",h:\"en timme\",hh:\"%d timmar\",d:\"en dag\",dd:\"%d dagar\",M:\"en månad\",MM:\"%d månader\",y:\"ett år\",yy:\"%d år\"}};return a.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL3N2LmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyxzWkFBc1osV0FBVyx1Q0FBdUMsVUFBVSwrS0FBK0ssZUFBZSw2TEFBNkwscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9kYXlqcy9sb2NhbGUvc3YuanM/MTUyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIhZnVuY3Rpb24oZSx0KXtcIm9iamVjdFwiPT10eXBlb2YgZXhwb3J0cyYmXCJ1bmRlZmluZWRcIiE9dHlwZW9mIG1vZHVsZT9tb2R1bGUuZXhwb3J0cz10KHJlcXVpcmUoXCJkYXlqc1wiKSk6XCJmdW5jdGlvblwiPT10eXBlb2YgZGVmaW5lJiZkZWZpbmUuYW1kP2RlZmluZShbXCJkYXlqc1wiXSx0KTooZT1cInVuZGVmaW5lZFwiIT10eXBlb2YgZ2xvYmFsVGhpcz9nbG9iYWxUaGlzOmV8fHNlbGYpLmRheWpzX2xvY2FsZV9zdj10KGUuZGF5anMpfSh0aGlzLChmdW5jdGlvbihlKXtcInVzZSBzdHJpY3RcIjtmdW5jdGlvbiB0KGUpe3JldHVybiBlJiZcIm9iamVjdFwiPT10eXBlb2YgZSYmXCJkZWZhdWx0XCJpbiBlP2U6e2RlZmF1bHQ6ZX19dmFyIGE9dChlKSxkPXtuYW1lOlwic3ZcIix3ZWVrZGF5czpcInPDtm5kYWdfbcOlbmRhZ190aXNkYWdfb25zZGFnX3RvcnNkYWdfZnJlZGFnX2zDtnJkYWdcIi5zcGxpdChcIl9cIiksd2Vla2RheXNTaG9ydDpcInPDtm5fbcOlbl90aXNfb25zX3Rvcl9mcmVfbMO2clwiLnNwbGl0KFwiX1wiKSx3ZWVrZGF5c01pbjpcInPDtl9tw6VfdGlfb25fdG9fZnJfbMO2XCIuc3BsaXQoXCJfXCIpLG1vbnRoczpcImphbnVhcmlfZmVicnVhcmlfbWFyc19hcHJpbF9tYWpfanVuaV9qdWxpX2F1Z3VzdGlfc2VwdGVtYmVyX29rdG9iZXJfbm92ZW1iZXJfZGVjZW1iZXJcIi5zcGxpdChcIl9cIiksbW9udGhzU2hvcnQ6XCJqYW5fZmViX21hcl9hcHJfbWFqX2p1bl9qdWxfYXVnX3NlcF9va3Rfbm92X2RlY1wiLnNwbGl0KFwiX1wiKSx3ZWVrU3RhcnQ6MSx5ZWFyU3RhcnQ6NCxvcmRpbmFsOmZ1bmN0aW9uKGUpe3ZhciB0PWUlMTA7cmV0dXJuXCJbXCIrZSsoMT09PXR8fDI9PT10P1wiYVwiOlwiZVwiKStcIl1cIn0sZm9ybWF0czp7TFQ6XCJISDptbVwiLExUUzpcIkhIOm1tOnNzXCIsTDpcIllZWVktTU0tRERcIixMTDpcIkQgTU1NTSBZWVlZXCIsTExMOlwiRCBNTU1NIFlZWVkgW2tsLl0gSEg6bW1cIixMTExMOlwiZGRkZCBEIE1NTU0gWVlZWSBba2wuXSBISDptbVwiLGxsbDpcIkQgTU1NIFlZWVkgSEg6bW1cIixsbGxsOlwiZGRkIEQgTU1NIFlZWVkgSEg6bW1cIn0scmVsYXRpdmVUaW1lOntmdXR1cmU6XCJvbSAlc1wiLHBhc3Q6XCJmw7ZyICVzIHNlZGFuXCIsczpcIm7DpWdyYSBzZWt1bmRlclwiLG06XCJlbiBtaW51dFwiLG1tOlwiJWQgbWludXRlclwiLGg6XCJlbiB0aW1tZVwiLGhoOlwiJWQgdGltbWFyXCIsZDpcImVuIGRhZ1wiLGRkOlwiJWQgZGFnYXJcIixNOlwiZW4gbcOlbmFkXCIsTU06XCIlZCBtw6VuYWRlclwiLHk6XCJldHQgw6VyXCIseXk6XCIlZCDDpXJcIn19O3JldHVybiBhLmRlZmF1bHQubG9jYWxlKGQsbnVsbCwhMCksZH0pKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/sv.js\n"));

/***/ })

}]);