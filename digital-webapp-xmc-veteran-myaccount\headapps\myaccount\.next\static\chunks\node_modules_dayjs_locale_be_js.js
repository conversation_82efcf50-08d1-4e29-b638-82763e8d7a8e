/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_be_js"],{

/***/ "./node_modules/dayjs/locale/be.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/be.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),n={name:\"be\",weekdays:\"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу\".split(\"_\"),months:\"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня\".split(\"_\"),weekStart:1,weekdaysShort:\"нд_пн_ат_ср_чц_пт_сб\".split(\"_\"),monthsShort:\"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж\".split(\"_\"),weekdaysMin:\"нд_пн_ат_ср_чц_пт_сб\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY г.\",LLL:\"D MMMM YYYY г., HH:mm\",LLLL:\"dddd, D MMMM YYYY г., HH:mm\"}};return t.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/be.js\n"));

/***/ })

}]);