/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_fr-ch_js"],{

/***/ "./node_modules/dayjs/locale/fr-ch.js":
/*!********************************************!*\
  !*** ./node_modules/dayjs/locale/fr-ch.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,n){ true?module.exports=n(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=n(e),_={name:\"fr-ch\",weekdays:\"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi\".split(\"_\"),months:\"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre\".split(\"_\"),weekStart:1,weekdaysShort:\"dim._lun._mar._mer._jeu._ven._sam.\".split(\"_\"),monthsShort:\"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.\".split(\"_\"),weekdaysMin:\"di_lu_ma_me_je_ve_sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"dans %s\",past:\"il y a %s\",s:\"quelques secondes\",m:\"une minute\",mm:\"%d minutes\",h:\"une heure\",hh:\"%d heures\",d:\"un jour\",dd:\"%d jours\",M:\"un mois\",MM:\"%d mois\",y:\"un an\",yy:\"%d ans\"}};return i.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/fr-ch.js\n"));

/***/ })

}]);