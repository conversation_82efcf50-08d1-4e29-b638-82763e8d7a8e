/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ru_js"],{

/***/ "./node_modules/dayjs/locale/ru.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ru.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function t(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var e=t(_),n=\"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря\".split(\"_\"),s=\"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь\".split(\"_\"),r=\"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.\".split(\"_\"),o=\"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.\".split(\"_\"),i=/D[oD]?(\\[[^[\\]]*\\]|\\s)+MMMM?/;function d(_,t,e){var n,s;return\"m\"===e?t?\"минута\":\"минуту\":_+\" \"+(n=+_,s={mm:t?\"минута_минуты_минут\":\"минуту_минуты_минут\",hh:\"час_часа_часов\",dd:\"день_дня_дней\",MM:\"месяц_месяца_месяцев\",yy:\"год_года_лет\"}[e].split(\"_\"),n%10==1&&n%100!=11?s[0]:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?s[1]:s[2])}var u=function(_,t){return i.test(t)?n[_.month()]:s[_.month()]};u.s=s,u.f=n;var a=function(_,t){return i.test(t)?r[_.month()]:o[_.month()]};a.s=o,a.f=r;var m={name:\"ru\",weekdays:\"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота\".split(\"_\"),weekdaysShort:\"вск_пнд_втр_срд_чтв_птн_сбт\".split(\"_\"),weekdaysMin:\"вс_пн_вт_ср_чт_пт_сб\".split(\"_\"),months:u,monthsShort:a,weekStart:1,yearStart:4,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY г.\",LLL:\"D MMMM YYYY г., H:mm\",LLLL:\"dddd, D MMMM YYYY г., H:mm\"},relativeTime:{future:\"через %s\",past:\"%s назад\",s:\"несколько секунд\",m:d,mm:d,h:\"час\",hh:d,d:\"день\",dd:d,M:\"месяц\",MM:d,y:\"год\",yy:d},ordinal:function(_){return _},meridiem:function(_){return _<4?\"ночи\":_<12?\"утра\":_<17?\"дня\":\"вечера\"}};return e.default.locale(m,null,!0),m}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ru.js\n"));

/***/ })

}]);