/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_vi_js"],{

/***/ "./node_modules/dayjs/locale/vi.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/vi.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(t,n){ true?module.exports=n(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(t){\"use strict\";function n(t){return t&&\"object\"==typeof t&&\"default\"in t?t:{default:t}}var h=n(t),_={name:\"vi\",weekdays:\"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy\".split(\"_\"),months:\"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12\".split(\"_\"),weekStart:1,weekdaysShort:\"CN_T2_T3_T4_T5_T6_T7\".split(\"_\"),monthsShort:\"Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12\".split(\"_\"),weekdaysMin:\"CN_T2_T3_T4_T5_T6_T7\".split(\"_\"),ordinal:function(t){return t},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM [năm] YYYY\",LLL:\"D MMMM [năm] YYYY HH:mm\",LLLL:\"dddd, D MMMM [năm] YYYY HH:mm\",l:\"DD/M/YYYY\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd, D MMM YYYY HH:mm\"},relativeTime:{future:\"%s tới\",past:\"%s trước\",s:\"vài giây\",m:\"một phút\",mm:\"%d phút\",h:\"một giờ\",hh:\"%d giờ\",d:\"một ngày\",dd:\"%d ngày\",M:\"một tháng\",MM:\"%d tháng\",y:\"một năm\",yy:\"%d năm\"}};return h.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/vi.js\n"));

/***/ })

}]);