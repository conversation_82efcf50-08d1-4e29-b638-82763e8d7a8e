/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_el_js"],{

/***/ "./node_modules/dayjs/locale/el.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/el.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,_){ true?module.exports=_(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"el\",weekdays:\"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο\".split(\"_\"),weekdaysShort:\"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ\".split(\"_\"),weekdaysMin:\"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα\".split(\"_\"),months:\"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος\".split(\"_\"),monthsShort:\"Ιαν_Φεβ_Μαρ_Απρ_Μαι_Ιουν_Ιουλ_Αυγ_Σεπτ_Οκτ_Νοε_Δεκ\".split(\"_\"),ordinal:function(e){return e},weekStart:1,relativeTime:{future:\"σε %s\",past:\"πριν %s\",s:\"μερικά δευτερόλεπτα\",m:\"ένα λεπτό\",mm:\"%d λεπτά\",h:\"μία ώρα\",hh:\"%d ώρες\",d:\"μία μέρα\",dd:\"%d μέρες\",M:\"ένα μήνα\",MM:\"%d μήνες\",y:\"ένα χρόνο\",yy:\"%d χρόνια\"},formats:{LT:\"h:mm A\",LTS:\"h:mm:ss A\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY h:mm A\",LLLL:\"dddd, D MMMM YYYY h:mm A\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/el.js\n"));

/***/ })

}]);