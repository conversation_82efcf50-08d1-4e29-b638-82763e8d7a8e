/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_gom-latn_js"],{

/***/ "./node_modules/dayjs/locale/gom-latn.js":
/*!***********************************************!*\
  !*** ./node_modules/dayjs/locale/gom-latn.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=t(e),_={name:\"gom-latn\",weekdays:\"Aitar_Somar_Mongllar_Budvar_Brestar_Sukrar_Son'var\".split(\"_\"),months:\"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr\".split(\"_\"),weekStart:1,weekdaysShort:\"Ait._Som._Mon._Bud._Bre._Suk._Son.\".split(\"_\"),monthsShort:\"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.\".split(\"_\"),weekdaysMin:\"Ai_Sm_Mo_Bu_Br_Su_Sn\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"A h:mm [vazta]\",LTS:\"A h:mm:ss [vazta]\",L:\"DD-MM-YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY A h:mm [vazta]\",LLLL:\"dddd, MMMM[achea] Do, YYYY, A h:mm [vazta]\",llll:\"ddd, D MMM YYYY, A h:mm [vazta]\"}};return a.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL2dvbS1sYXRuLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUFnSixDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyxzWkFBc1osU0FBUyxVQUFVLHdNQUF3TSxxQ0FBcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2RheWpzL2xvY2FsZS9nb20tbGF0bi5qcz9lNTljIl0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihlLHQpe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPXQocmVxdWlyZShcImRheWpzXCIpKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKFtcImRheWpzXCJdLHQpOihlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuZGF5anNfbG9jYWxlX2dvbV9sYXRuPXQoZS5kYXlqcyl9KHRoaXMsKGZ1bmN0aW9uKGUpe1widXNlIHN0cmljdFwiO2Z1bmN0aW9uIHQoZSl7cmV0dXJuIGUmJlwib2JqZWN0XCI9PXR5cGVvZiBlJiZcImRlZmF1bHRcImluIGU/ZTp7ZGVmYXVsdDplfX12YXIgYT10KGUpLF89e25hbWU6XCJnb20tbGF0blwiLHdlZWtkYXlzOlwiQWl0YXJfU29tYXJfTW9uZ2xsYXJfQnVkdmFyX0JyZXN0YXJfU3VrcmFyX1Nvbid2YXJcIi5zcGxpdChcIl9cIiksbW9udGhzOlwiSmFuZXJfRmVicmVyX01hcnNfQWJyaWxfTWFpX0p1bl9KdWxhaV9BZ29zdF9TZXRlbWJyX090dWJyX05vdmVtYnJfRGV6ZW1iclwiLnNwbGl0KFwiX1wiKSx3ZWVrU3RhcnQ6MSx3ZWVrZGF5c1Nob3J0OlwiQWl0Ll9Tb20uX01vbi5fQnVkLl9CcmUuX1N1ay5fU29uLlwiLnNwbGl0KFwiX1wiKSxtb250aHNTaG9ydDpcIkphbi5fRmViLl9NYXJzX0Fici5fTWFpX0p1bl9KdWwuX0Fnby5fU2V0Ll9PdHUuX05vdi5fRGV6LlwiLnNwbGl0KFwiX1wiKSx3ZWVrZGF5c01pbjpcIkFpX1NtX01vX0J1X0JyX1N1X1NuXCIuc3BsaXQoXCJfXCIpLG9yZGluYWw6ZnVuY3Rpb24oZSl7cmV0dXJuIGV9LGZvcm1hdHM6e0xUOlwiQSBoOm1tIFt2YXp0YV1cIixMVFM6XCJBIGg6bW06c3MgW3ZhenRhXVwiLEw6XCJERC1NTS1ZWVlZXCIsTEw6XCJEIE1NTU0gWVlZWVwiLExMTDpcIkQgTU1NTSBZWVlZIEEgaDptbSBbdmF6dGFdXCIsTExMTDpcImRkZGQsIE1NTU1bYWNoZWFdIERvLCBZWVlZLCBBIGg6bW0gW3ZhenRhXVwiLGxsbGw6XCJkZGQsIEQgTU1NIFlZWVksIEEgaDptbSBbdmF6dGFdXCJ9fTtyZXR1cm4gYS5kZWZhdWx0LmxvY2FsZShfLG51bGwsITApLF99KSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/gom-latn.js\n"));

/***/ })

}]);