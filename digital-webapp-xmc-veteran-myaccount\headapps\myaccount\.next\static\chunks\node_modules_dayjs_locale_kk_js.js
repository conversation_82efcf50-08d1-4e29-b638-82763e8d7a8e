/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_kk_js"],{

/***/ "./node_modules/dayjs/locale/kk.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/kk.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"kk\",weekdays:\"жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі\".split(\"_\"),weekdaysShort:\"жек_дүй_сей_сәр_бей_жұм_сен\".split(\"_\"),weekdaysMin:\"жк_дй_сй_ср_бй_жм_сн\".split(\"_\"),months:\"қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан\".split(\"_\"),monthsShort:\"қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел\".split(\"_\"),weekStart:1,relativeTime:{future:\"%s ішінде\",past:\"%s бұрын\",s:\"бірнеше секунд\",m:\"бір минут\",mm:\"%d минут\",h:\"бір сағат\",hh:\"%d сағат\",d:\"бір күн\",dd:\"%d күн\",M:\"бір ай\",MM:\"%d ай\",y:\"бір жыл\",yy:\"%d жыл\"},ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL2trLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyx1WUFBdVksb0xBQW9MLHFCQUFxQixTQUFTLFVBQVUsbUhBQW1ILHFDQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL2trLmpzPzY3NGMiXSwic291cmNlc0NvbnRlbnQiOlsiIWZ1bmN0aW9uKF8sZSl7XCJvYmplY3RcIj09dHlwZW9mIGV4cG9ydHMmJlwidW5kZWZpbmVkXCIhPXR5cGVvZiBtb2R1bGU/bW9kdWxlLmV4cG9ydHM9ZShyZXF1aXJlKFwiZGF5anNcIikpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUoW1wiZGF5anNcIl0sZSk6KF89XCJ1bmRlZmluZWRcIiE9dHlwZW9mIGdsb2JhbFRoaXM/Z2xvYmFsVGhpczpffHxzZWxmKS5kYXlqc19sb2NhbGVfa2s9ZShfLmRheWpzKX0odGhpcywoZnVuY3Rpb24oXyl7XCJ1c2Ugc3RyaWN0XCI7ZnVuY3Rpb24gZShfKXtyZXR1cm4gXyYmXCJvYmplY3RcIj09dHlwZW9mIF8mJlwiZGVmYXVsdFwiaW4gXz9fOntkZWZhdWx0Ol99fXZhciB0PWUoXyksZD17bmFtZTpcImtrXCIsd2Vla2RheXM6XCLQttC10LrRgdC10L3QsdGWX9C00q/QudGB0LXQvdCx0ZZf0YHQtdC50YHQtdC90LHRll/RgdOZ0YDRgdC10L3QsdGWX9Cx0LXQudGB0LXQvdCx0ZZf0LbSsdC80LBf0YHQtdC90LHRllwiLnNwbGl0KFwiX1wiKSx3ZWVrZGF5c1Nob3J0Olwi0LbQtdC6X9C00q/QuV/RgdC10Llf0YHTmdGAX9Cx0LXQuV/QttKx0Lxf0YHQtdC9XCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzTWluOlwi0LbQul/QtNC5X9GB0Llf0YHRgF/QsdC5X9C20Lxf0YHQvVwiLnNwbGl0KFwiX1wiKSxtb250aHM6XCLSm9Cw0qPRgtCw0YBf0LDSm9C/0LDQvV/QvdCw0YPRgNGL0Ldf0YHTmdGD0ZbRgF/QvNCw0LzRi9GAX9C80LDRg9GB0YvQvF/RiNGW0LvQtNC1X9GC0LDQvNGL0Ldf0pvRi9GA0LrSr9C50LXQul/Sm9Cw0LfQsNC9X9Kb0LDRgNCw0YjQsF/QttC10LvRgtC+0pvRgdCw0L1cIi5zcGxpdChcIl9cIiksbW9udGhzU2hvcnQ6XCLSm9Cw0qNf0LDSm9C/X9C90LDRg1/RgdOZ0YNf0LzQsNC8X9C80LDRg1/RiNGW0Ltf0YLQsNC8X9Kb0YvRgF/Sm9Cw0Ldf0pvQsNGAX9C20LXQu1wiLnNwbGl0KFwiX1wiKSx3ZWVrU3RhcnQ6MSxyZWxhdGl2ZVRpbWU6e2Z1dHVyZTpcIiVzINGW0YjRltC90LTQtVwiLHBhc3Q6XCIlcyDQsdKx0YDRi9C9XCIsczpcItCx0ZbRgNC90LXRiNC1INGB0LXQutGD0L3QtFwiLG06XCLQsdGW0YAg0LzQuNC90YPRglwiLG1tOlwiJWQg0LzQuNC90YPRglwiLGg6XCLQsdGW0YAg0YHQsNKT0LDRglwiLGhoOlwiJWQg0YHQsNKT0LDRglwiLGQ6XCLQsdGW0YAg0LrSr9C9XCIsZGQ6XCIlZCDQutKv0L1cIixNOlwi0LHRltGAINCw0LlcIixNTTpcIiVkINCw0LlcIix5Olwi0LHRltGAINC20YvQu1wiLHl5OlwiJWQg0LbRi9C7XCJ9LG9yZGluYWw6ZnVuY3Rpb24oXyl7cmV0dXJuIF99LGZvcm1hdHM6e0xUOlwiSEg6bW1cIixMVFM6XCJISDptbTpzc1wiLEw6XCJERC5NTS5ZWVlZXCIsTEw6XCJEIE1NTU0gWVlZWVwiLExMTDpcIkQgTU1NTSBZWVlZIEhIOm1tXCIsTExMTDpcImRkZGQsIEQgTU1NTSBZWVlZIEhIOm1tXCJ9fTtyZXR1cm4gdC5kZWZhdWx0LmxvY2FsZShkLG51bGwsITApLGR9KSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/kk.js\n"));

/***/ })

}]);