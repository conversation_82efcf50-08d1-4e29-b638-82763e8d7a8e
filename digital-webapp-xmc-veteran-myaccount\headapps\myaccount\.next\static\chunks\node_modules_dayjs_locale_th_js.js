/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_th_js"],{

/***/ "./node_modules/dayjs/locale/th.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/th.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"th\",weekdays:\"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์\".split(\"_\"),weekdaysShort:\"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์\".split(\"_\"),weekdaysMin:\"อา._จ._อ._พ._พฤ._ศ._ส.\".split(\"_\"),months:\"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม\".split(\"_\"),monthsShort:\"ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.\".split(\"_\"),formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY เวลา H:mm\",LLLL:\"วันddddที่ D MMMM YYYY เวลา H:mm\"},relativeTime:{future:\"อีก %s\",past:\"%sที่แล้ว\",s:\"ไม่กี่วินาที\",m:\"1 นาที\",mm:\"%d นาที\",h:\"1 ชั่วโมง\",hh:\"%d ชั่วโมง\",d:\"1 วัน\",dd:\"%d วัน\",M:\"1 เดือน\",MM:\"%d เดือน\",y:\"1 ปี\",yy:\"%d ปี\"},ordinal:function(_){return _+\".\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/th.js\n"));

/***/ })

}]);