/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_tzm_js"],{

/***/ "./node_modules/dayjs/locale/tzm.js":
/*!******************************************!*\
  !*** ./node_modules/dayjs/locale/tzm.js ***!
  \******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"tzm\",weekdays:\"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ\".split(\"_\"),months:\"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ\".split(\"_\"),weekStart:6,weekdaysShort:\"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ\".split(\"_\"),monthsShort:\"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ\".split(\"_\"),weekdaysMin:\"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s\",past:\"ⵢⴰⵏ %s\",s:\"ⵉⵎⵉⴽ\",m:\"ⵎⵉⵏⵓⴺ\",mm:\"%d ⵎⵉⵏⵓⴺ\",h:\"ⵙⴰⵄⴰ\",hh:\"%d ⵜⴰⵙⵙⴰⵄⵉⵏ\",d:\"ⴰⵙⵙ\",dd:\"%d oⵙⵙⴰⵏ\",M:\"ⴰⵢoⵓⵔ\",MM:\"%d ⵉⵢⵢⵉⵔⵏ\",y:\"ⴰⵙⴳⴰⵙ\",yy:\"%d ⵉⵙⴳⴰⵙⵏ\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/tzm.js\n"));

/***/ })

}]);