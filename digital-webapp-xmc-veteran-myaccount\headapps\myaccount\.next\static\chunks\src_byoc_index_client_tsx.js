"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_byoc_index_client_tsx"],{

/***/ "./src/byoc/index.client.tsx":
/*!***********************************!*\
  !*** ./src/byoc/index.client.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-feaas/clientside/react */ \"./node_modules/@sitecore-feaas/clientside/dist/browser/react.esm.js\");\n/* harmony import */ var _sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @sitecore/components/form */ \"./node_modules/@sitecore/components/dist/esm/browser/form/index.js\");\n\n/**\r\n * Below are Sitecore default BYOC components. Included components will be available in Pages and Components apps out of the\r\n * box for convenience. It is advised to comment out unused components when application is ready for production\r\n * to reduce javascript bundle size.\r\n */ // SitecoreForm component displays forms created in XM Forms as individual components to be embedded into Pages.\n// Sitecore Forms for Sitecore XP are still available separately via @sitecore-jss-forms package\n\n/**\r\n * End of built-in JSS imports\r\n * You can import your own client component below\r\n * @example\r\n * import './MyClientComponent';\r\n * @example\r\n * import 'src/otherFolder/MyOtherComponent';\r\n */ // An important boilerplate component that prevents BYOC components from being optimized away and allows then. Should be kept in this file.\nconst ClientsideComponent = (props)=>_sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__.ExternalComponent(props);\n_c = ClientsideComponent;\n/**\r\n * Clientside BYOC component will be rendered in the browser, so that external components:\r\n * - Can have access to DOM apis, including network requests\r\n * - Use clientside react hooks like useEffect.\r\n * - Be implemented as web components.\r\n */ /* harmony default export */ __webpack_exports__[\"default\"] = (ClientsideComponent);\nvar _c;\n$RefreshReg$(_c, \"ClientsideComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/byoc/index.client.tsx\n"));

/***/ }),

/***/ "./node_modules/@sitecore/components/dist/esm/browser/form/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@sitecore/components/dist/esm/browser/form/index.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _sitecore_byoc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore/byoc */ \"./node_modules/@sitecore/byoc/dist/browser/index.esm.js\");\n/* harmony import */ var _web_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./web.js */ \"./node_modules/@sitecore/components/dist/esm/browser/form/web.js\");\n_sitecore_byoc__WEBPACK_IMPORTED_MODULE_0__.registerComponent(_web_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],{name:\"SitecoreForm\",isHidden:!0,title:\"Sitecore Form\",links:{github:\"https://github.com/Sitecore-PD/sitecore.cloudsdk.components/tree/master/src/form\",npm:\"https://www.npmjs.com/package/@sitecore/components\",icon:\"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20id='forms'%3e%3crect%20id='Rectangle%208'%20x='2'%20y='4'%20width='20'%20height='7'%20rx='0.5'%20fill='%23AAA4EC'/%3e%3crect%20id='Rectangle%209'%20x='2'%20y='12.5'%20width='12'%20height='7'%20rx='0.5'%20fill='%235548D9'/%3e%3c/g%3e%3c/svg%3e\"},properties:{formId:{type:\"string\"}}});\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHNpdGVjb3JlL2NvbXBvbmVudHMvZGlzdC9lc20vYnJvd3Nlci9mb3JtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7OztBQUF5RCw2REFBbUIsQ0FBQywrQ0FBQyxFQUFFLDZEQUE2RCwwaEJBQTBoQixhQUFhLFFBQVEsZ0JBQWdCO0FBQzVzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHNpdGVjb3JlL2NvbXBvbmVudHMvZGlzdC9lc20vYnJvd3Nlci9mb3JtL2luZGV4LmpzP2NjMjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KmFzIGUgZnJvbVwiQHNpdGVjb3JlL2J5b2NcIjtpbXBvcnQgdCBmcm9tXCIuL3dlYi5qc1wiO2UucmVnaXN0ZXJDb21wb25lbnQodCx7bmFtZTpcIlNpdGVjb3JlRm9ybVwiLGlzSGlkZGVuOiEwLHRpdGxlOlwiU2l0ZWNvcmUgRm9ybVwiLGxpbmtzOntnaXRodWI6XCJodHRwczovL2dpdGh1Yi5jb20vU2l0ZWNvcmUtUEQvc2l0ZWNvcmUuY2xvdWRzZGsuY29tcG9uZW50cy90cmVlL21hc3Rlci9zcmMvZm9ybVwiLG5wbTpcImh0dHBzOi8vd3d3Lm5wbWpzLmNvbS9wYWNrYWdlL0BzaXRlY29yZS9jb21wb25lbnRzXCIsaWNvbjpcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmclMjB3aWR0aD0nMjQnJTIwaGVpZ2h0PScyNCclMjB2aWV3Qm94PScwJTIwMCUyMDI0JTIwMjQnJTIwZmlsbD0nbm9uZSclMjB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnJTNlJTNjZyUyMGlkPSdmb3JtcyclM2UlM2NyZWN0JTIwaWQ9J1JlY3RhbmdsZSUyMDgnJTIweD0nMiclMjB5PSc0JyUyMHdpZHRoPScyMCclMjBoZWlnaHQ9JzcnJTIwcng9JzAuNSclMjBmaWxsPSclMjNBQUE0RUMnLyUzZSUzY3JlY3QlMjBpZD0nUmVjdGFuZ2xlJTIwOSclMjB4PScyJyUyMHk9JzEyLjUnJTIwd2lkdGg9JzEyJyUyMGhlaWdodD0nNyclMjByeD0nMC41JyUyMGZpbGw9JyUyMzU1NDhEOScvJTNlJTNjL2clM2UlM2Mvc3ZnJTNlXCJ9LHByb3BlcnRpZXM6e2Zvcm1JZDp7dHlwZTpcInN0cmluZ1wifX19KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@sitecore/components/dist/esm/browser/form/index.js\n"));

/***/ }),

/***/ "./node_modules/@sitecore/components/dist/esm/browser/form/sdk.js":
/*!************************************************************************!*\
  !*** ./node_modules/@sitecore/components/dist/esm/browser/form/sdk.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadForm: function() { return /* binding */ w; }\n/* harmony export */ });\nvar s=function(e,n,t,o){function c(r){return r instanceof t?r:new t(function(d){d(r)})}return new(t||(t=Promise))(function(r,d){function f(i){try{l(o.next(i))}catch(a){d(a)}}function h(i){try{l(o.throw(i))}catch(a){d(a)}}function l(i){i.done?r(i.value):c(i.value).then(f,h)}l((o=o.apply(e,n||[])).next())})};const u={baseUrl:e=>`https://forms-publisher-${e||\"euw\"}.sitecorecloud.io`,path:\"/api/v1/forms\",edgePath:\"/v1/forms/publisher\"};function m(e){const n=e.querySelectorAll(\"script\");Array.from(n).forEach(t=>{var o;const c=document.createElement(\"script\");Array.from(t.attributes).forEach(r=>{c.setAttribute(r.name,r.value)}),c.text=t.text,(o=t==null?void 0:t.parentNode)===null||o===void 0||o.replaceChild(c,t)})}function v(e,n,t){return new URL(n).origin+u.edgePath+\"/\"+e+\"?sitecoreContextId=\"+t}function g(e){const n=e.split(\"-\");return n[n.length-1]}function p(e){const n=g(e);return u.baseUrl(n)+u.path+\"/\"+e}function x(e){return s(this,void 0,void 0,function*(){return yield(yield fetch(e,{method:\"GET\",cache:\"no-cache\"})).text()})}const w=(e,n,t)=>s(void 0,void 0,void 0,function*(){let o;t&&t.contextId&&t.edgeUrl?o=v(e,t.edgeUrl,t.contextId):o=p(e);const c=yield x(o);if(n)n.innerHTML=c,m(n);else throw new Error(\"Second parameter of loadForm needs to be a valid DOM node\")});\n//# sourceMappingURL=sdk.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@sitecore/components/dist/esm/browser/form/sdk.js\n"));

/***/ }),

/***/ "./node_modules/@sitecore/components/dist/esm/browser/form/web.js":
/*!************************************************************************!*\
  !*** ./node_modules/@sitecore/components/dist/esm/browser/form/web.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ n; }\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"./node_modules/@sitecore/components/dist/esm/browser/form/sdk.js\");\nconst l=typeof HTMLElement!=\"undefined\"?HTMLElement:class{setAttribute(){}};class n extends l{sitecoreContextCallback(e){this.addEventListener(\"form:engage\",t=>{var o,i;e.pageState===\"preview\"||e.pageState===\"edit\"?console.log(\"form:engage\",t.detail.formId,t.detail.name,e.uid):(o=e.eventsSDK)===null||o===void 0||o.form(t.detail.formId,t.detail.name,((i=e.uid)===null||i===void 0?void 0:i.replace(/-/g,\"\"))||\"\")})}connectedCallback(){var e,t,o,i;const d=this.getAttribute(\"form-id\");if(!d){console.error(\"Sitecore Form requires form-id attribute to be set.\");return}(0,_sdk_js__WEBPACK_IMPORTED_MODULE_0__.loadForm)(d,this,{edgeUrl:(t=(e=this.getAttribute(\"sitecore-edge-url\"))===null||e===void 0?void 0:e.toString())!==null&&t!==void 0?t:null,contextId:(i=(o=this.getAttribute(\"sitecore-edge-context-id\"))===null||o===void 0?void 0:o.toString())!==null&&i!==void 0?i:null})}}typeof window!=\"undefined\"&&!window.customElements.get(\"sitecore-form\")&&window.customElements.define(\"sitecore-form\",n);\n//# sourceMappingURL=web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHNpdGVjb3JlL2NvbXBvbmVudHMvZGlzdC9lc20vYnJvd3Nlci9mb3JtL3dlYi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQywwREFBMEQsa0JBQWtCLGtCQUFrQiwyQkFBMkIsd0NBQXdDLFFBQVEsb1BBQW9QLEVBQUUsb0JBQW9CLFlBQVkscUNBQXFDLE9BQU8scUVBQXFFLE9BQU8saURBQUMsU0FBUyx5UEFBeVAsR0FBRyx5SEFBOEk7QUFDLytCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ac2l0ZWNvcmUvY29tcG9uZW50cy9kaXN0L2VzbS9icm93c2VyL2Zvcm0vd2ViLmpzPzgzMzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2xvYWRGb3JtIGFzIHJ9ZnJvbVwiLi9zZGsuanNcIjtjb25zdCBsPXR5cGVvZiBIVE1MRWxlbWVudCE9XCJ1bmRlZmluZWRcIj9IVE1MRWxlbWVudDpjbGFzc3tzZXRBdHRyaWJ1dGUoKXt9fTtjbGFzcyBuIGV4dGVuZHMgbHtzaXRlY29yZUNvbnRleHRDYWxsYmFjayhlKXt0aGlzLmFkZEV2ZW50TGlzdGVuZXIoXCJmb3JtOmVuZ2FnZVwiLHQ9Pnt2YXIgbyxpO2UucGFnZVN0YXRlPT09XCJwcmV2aWV3XCJ8fGUucGFnZVN0YXRlPT09XCJlZGl0XCI/Y29uc29sZS5sb2coXCJmb3JtOmVuZ2FnZVwiLHQuZGV0YWlsLmZvcm1JZCx0LmRldGFpbC5uYW1lLGUudWlkKToobz1lLmV2ZW50c1NESyk9PT1udWxsfHxvPT09dm9pZCAwfHxvLmZvcm0odC5kZXRhaWwuZm9ybUlkLHQuZGV0YWlsLm5hbWUsKChpPWUudWlkKT09PW51bGx8fGk9PT12b2lkIDA/dm9pZCAwOmkucmVwbGFjZSgvLS9nLFwiXCIpKXx8XCJcIil9KX1jb25uZWN0ZWRDYWxsYmFjaygpe3ZhciBlLHQsbyxpO2NvbnN0IGQ9dGhpcy5nZXRBdHRyaWJ1dGUoXCJmb3JtLWlkXCIpO2lmKCFkKXtjb25zb2xlLmVycm9yKFwiU2l0ZWNvcmUgRm9ybSByZXF1aXJlcyBmb3JtLWlkIGF0dHJpYnV0ZSB0byBiZSBzZXQuXCIpO3JldHVybn1yKGQsdGhpcyx7ZWRnZVVybDoodD0oZT10aGlzLmdldEF0dHJpYnV0ZShcInNpdGVjb3JlLWVkZ2UtdXJsXCIpKT09PW51bGx8fGU9PT12b2lkIDA/dm9pZCAwOmUudG9TdHJpbmcoKSkhPT1udWxsJiZ0IT09dm9pZCAwP3Q6bnVsbCxjb250ZXh0SWQ6KGk9KG89dGhpcy5nZXRBdHRyaWJ1dGUoXCJzaXRlY29yZS1lZGdlLWNvbnRleHQtaWRcIikpPT09bnVsbHx8bz09PXZvaWQgMD92b2lkIDA6by50b1N0cmluZygpKSE9PW51bGwmJmkhPT12b2lkIDA/aTpudWxsfSl9fXR5cGVvZiB3aW5kb3chPVwidW5kZWZpbmVkXCImJiF3aW5kb3cuY3VzdG9tRWxlbWVudHMuZ2V0KFwic2l0ZWNvcmUtZm9ybVwiKSYmd2luZG93LmN1c3RvbUVsZW1lbnRzLmRlZmluZShcInNpdGVjb3JlLWZvcm1cIixuKTtleHBvcnR7biBhcyBkZWZhdWx0fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdlYi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@sitecore/components/dist/esm/browser/form/web.js\n"));

/***/ })

}]);