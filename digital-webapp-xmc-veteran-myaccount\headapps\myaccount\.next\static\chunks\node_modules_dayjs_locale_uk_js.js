/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_uk_js"],{

/***/ "./node_modules/dayjs/locale/uk.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/uk.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),s=\"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня\".split(\"_\"),n=\"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень\".split(\"_\"),o=/D[oD]?(\\[[^[\\]]*\\]|\\s)+MMMM?/;function d(_,e,t){var s,n;return\"m\"===t?e?\"хвилина\":\"хвилину\":\"h\"===t?e?\"година\":\"годину\":_+\" \"+(s=+_,n={ss:e?\"секунда_секунди_секунд\":\"секунду_секунди_секунд\",mm:e?\"хвилина_хвилини_хвилин\":\"хвилину_хвилини_хвилин\",hh:e?\"година_години_годин\":\"годину_години_годин\",dd:\"день_дні_днів\",MM:\"місяць_місяці_місяців\",yy:\"рік_роки_років\"}[t].split(\"_\"),s%10==1&&s%100!=11?n[0]:s%10>=2&&s%10<=4&&(s%100<10||s%100>=20)?n[1]:n[2])}var i=function(_,e){return o.test(e)?s[_.month()]:n[_.month()]};i.s=n,i.f=s;var r={name:\"uk\",weekdays:\"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота\".split(\"_\"),weekdaysShort:\"ндл_пнд_втр_срд_чтв_птн_сбт\".split(\"_\"),weekdaysMin:\"нд_пн_вт_ср_чт_пт_сб\".split(\"_\"),months:i,monthsShort:\"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд\".split(\"_\"),weekStart:1,relativeTime:{future:\"за %s\",past:\"%s тому\",s:\"декілька секунд\",m:d,mm:d,h:d,hh:d,d:\"день\",dd:d,M:\"місяць\",MM:d,y:\"рік\",yy:d},ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY р.\",LLL:\"D MMMM YYYY р., HH:mm\",LLLL:\"dddd, D MMMM YYYY р., HH:mm\"}};return t.default.locale(r,null,!0),r}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/uk.js\n"));

/***/ })

}]);