/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ro_js"],{

/***/ "./node_modules/dayjs/locale/ro.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ro.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,i){ true?module.exports=i(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function i(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=i(e),_={name:\"ro\",weekdays:\"Duminică_Luni_Marți_Miercuri_Joi_Vineri_Sâmbătă\".split(\"_\"),weekdaysShort:\"Dum_Lun_Mar_Mie_Joi_Vin_Sâm\".split(\"_\"),weekdaysMin:\"Du_Lu_Ma_Mi_Jo_Vi_Sâ\".split(\"_\"),months:\"Ianuarie_Februarie_Martie_Aprilie_Mai_Iunie_Iulie_August_Septembrie_Octombrie_Noiembrie_Decembrie\".split(\"_\"),monthsShort:\"Ian._Febr._Mart._Apr._Mai_Iun._Iul._Aug._Sept._Oct._Nov._Dec.\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY H:mm\",LLLL:\"dddd, D MMMM YYYY H:mm\"},relativeTime:{future:\"peste %s\",past:\"acum %s\",s:\"câteva secunde\",m:\"un minut\",mm:\"%d minute\",h:\"o oră\",hh:\"%d ore\",d:\"o zi\",dd:\"%d zile\",M:\"o lună\",MM:\"%d luni\",y:\"un an\",yy:\"%d ani\"},ordinal:function(e){return e}};return t.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL3JvLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyx1WkFBdVosNkdBQTZHLGVBQWUsMEtBQTBLLHFCQUFxQixXQUFXLHFDQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL3JvLmpzP2E5NWEiXSwic291cmNlc0NvbnRlbnQiOlsiIWZ1bmN0aW9uKGUsaSl7XCJvYmplY3RcIj09dHlwZW9mIGV4cG9ydHMmJlwidW5kZWZpbmVkXCIhPXR5cGVvZiBtb2R1bGU/bW9kdWxlLmV4cG9ydHM9aShyZXF1aXJlKFwiZGF5anNcIikpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUoW1wiZGF5anNcIl0saSk6KGU9XCJ1bmRlZmluZWRcIiE9dHlwZW9mIGdsb2JhbFRoaXM/Z2xvYmFsVGhpczplfHxzZWxmKS5kYXlqc19sb2NhbGVfcm89aShlLmRheWpzKX0odGhpcywoZnVuY3Rpb24oZSl7XCJ1c2Ugc3RyaWN0XCI7ZnVuY3Rpb24gaShlKXtyZXR1cm4gZSYmXCJvYmplY3RcIj09dHlwZW9mIGUmJlwiZGVmYXVsdFwiaW4gZT9lOntkZWZhdWx0OmV9fXZhciB0PWkoZSksXz17bmFtZTpcInJvXCIsd2Vla2RheXM6XCJEdW1pbmljxINfTHVuaV9NYXLIm2lfTWllcmN1cmlfSm9pX1ZpbmVyaV9Tw6JtYsSDdMSDXCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzU2hvcnQ6XCJEdW1fTHVuX01hcl9NaWVfSm9pX1Zpbl9Tw6JtXCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzTWluOlwiRHVfTHVfTWFfTWlfSm9fVmlfU8OiXCIuc3BsaXQoXCJfXCIpLG1vbnRoczpcIklhbnVhcmllX0ZlYnJ1YXJpZV9NYXJ0aWVfQXByaWxpZV9NYWlfSXVuaWVfSXVsaWVfQXVndXN0X1NlcHRlbWJyaWVfT2N0b21icmllX05vaWVtYnJpZV9EZWNlbWJyaWVcIi5zcGxpdChcIl9cIiksbW9udGhzU2hvcnQ6XCJJYW4uX0ZlYnIuX01hcnQuX0Fwci5fTWFpX0l1bi5fSXVsLl9BdWcuX1NlcHQuX09jdC5fTm92Ll9EZWMuXCIuc3BsaXQoXCJfXCIpLHdlZWtTdGFydDoxLGZvcm1hdHM6e0xUOlwiSDptbVwiLExUUzpcIkg6bW06c3NcIixMOlwiREQuTU0uWVlZWVwiLExMOlwiRCBNTU1NIFlZWVlcIixMTEw6XCJEIE1NTU0gWVlZWSBIOm1tXCIsTExMTDpcImRkZGQsIEQgTU1NTSBZWVlZIEg6bW1cIn0scmVsYXRpdmVUaW1lOntmdXR1cmU6XCJwZXN0ZSAlc1wiLHBhc3Q6XCJhY3VtICVzXCIsczpcImPDonRldmEgc2VjdW5kZVwiLG06XCJ1biBtaW51dFwiLG1tOlwiJWQgbWludXRlXCIsaDpcIm8gb3LEg1wiLGhoOlwiJWQgb3JlXCIsZDpcIm8gemlcIixkZDpcIiVkIHppbGVcIixNOlwibyBsdW7Eg1wiLE1NOlwiJWQgbHVuaVwiLHk6XCJ1biBhblwiLHl5OlwiJWQgYW5pXCJ9LG9yZGluYWw6ZnVuY3Rpb24oZSl7cmV0dXJuIGV9fTtyZXR1cm4gdC5kZWZhdWx0LmxvY2FsZShfLG51bGwsITApLF99KSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ro.js\n"));

/***/ })

}]);