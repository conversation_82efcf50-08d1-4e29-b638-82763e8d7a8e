/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_sr_js"],{

/***/ "./node_modules/dayjs/locale/sr.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/sr.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=t(e),r={words:{m:[\"jedan minut\",\"jednog minuta\"],mm:[\"%d minut\",\"%d minuta\",\"%d minuta\"],h:[\"jedan sat\",\"jednog sata\"],hh:[\"%d sat\",\"%d sata\",\"%d sati\"],d:[\"jedan dan\",\"jednog dana\"],dd:[\"%d dan\",\"%d dana\",\"%d dana\"],M:[\"jedan mesec\",\"jednog meseca\"],MM:[\"%d mesec\",\"%d meseca\",\"%d meseci\"],y:[\"jednu godinu\",\"jedne godine\"],yy:[\"%d godinu\",\"%d godine\",\"%d godina\"]},correctGrammarCase:function(e,t){return e%10>=1&&e%10<=4&&(e%100<10||e%100>=20)?e%10==1?t[0]:t[1]:t[2]},relativeTimeFormatter:function(e,t,a,d){var n=r.words[a];if(1===a.length)return\"y\"===a&&t?\"jedna godina\":d||t?n[0]:n[1];var i=r.correctGrammarCase(e,n);return\"yy\"===a&&t&&\"%d godinu\"===i?e+\" godina\":i.replace(\"%d\",e)}},d={name:\"sr\",weekdays:\"Nedelja_Ponedeljak_Utorak_Sreda_Četvrtak_Petak_Subota\".split(\"_\"),weekdaysShort:\"Ned._Pon._Uto._Sre._Čet._Pet._Sub.\".split(\"_\"),weekdaysMin:\"ne_po_ut_sr_če_pe_su\".split(\"_\"),months:\"Januar_Februar_Mart_April_Maj_Jun_Jul_Avgust_Septembar_Oktobar_Novembar_Decembar\".split(\"_\"),monthsShort:\"Jan._Feb._Mar._Apr._Maj_Jun_Jul_Avg._Sep._Okt._Nov._Dec.\".split(\"_\"),weekStart:1,relativeTime:{future:\"za %s\",past:\"pre %s\",s:\"nekoliko sekundi\",m:r.relativeTimeFormatter,mm:r.relativeTimeFormatter,h:r.relativeTimeFormatter,hh:r.relativeTimeFormatter,d:r.relativeTimeFormatter,dd:r.relativeTimeFormatter,M:r.relativeTimeFormatter,MM:r.relativeTimeFormatter,y:r.relativeTimeFormatter,yy:r.relativeTimeFormatter},ordinal:function(e){return e+\".\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"D. M. YYYY.\",LL:\"D. MMMM YYYY.\",LLL:\"D. MMMM YYYY. H:mm\",LLLL:\"dddd, D. MMMM YYYY. H:mm\"}};return a.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/sr.js\n"));

/***/ })

}]);