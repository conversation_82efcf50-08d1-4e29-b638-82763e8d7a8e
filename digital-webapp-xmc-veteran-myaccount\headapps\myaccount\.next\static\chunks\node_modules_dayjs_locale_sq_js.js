/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_sq_js"],{

/***/ "./node_modules/dayjs/locale/sq.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/sq.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=t(e),n={name:\"sq\",weekdays:\"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë\".split(\"_\"),months:\"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor\".split(\"_\"),weekStart:1,weekdaysShort:\"Die_Hën_Mar_Mër_Enj_Pre_Sht\".split(\"_\"),monthsShort:\"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj\".split(\"_\"),weekdaysMin:\"D_H_Ma_Më_E_P_Sh\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"në %s\",past:\"%s më parë\",s:\"disa sekonda\",m:\"një minutë\",mm:\"%d minuta\",h:\"një orë\",hh:\"%d orë\",d:\"një ditë\",dd:\"%d ditë\",M:\"një muaj\",MM:\"%d muaj\",y:\"një vit\",yy:\"%d vite\"}};return _.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/sq.js\n"));

/***/ })

}]);