/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ka_js"],{

/***/ "./node_modules/dayjs/locale/ka.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ka.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ka\",weekdays:\"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი\".split(\"_\"),weekdaysShort:\"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ\".split(\"_\"),weekdaysMin:\"კვ_ორ_სა_ოთ_ხუ_პა_შა\".split(\"_\"),months:\"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი\".split(\"_\"),monthsShort:\"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ\".split(\"_\"),weekStart:1,formats:{LT:\"h:mm A\",LTS:\"h:mm:ss A\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY h:mm A\",LLLL:\"dddd, D MMMM YYYY h:mm A\"},relativeTime:{future:\"%s შემდეგ\",past:\"%s წინ\",s:\"წამი\",m:\"წუთი\",mm:\"%d წუთი\",h:\"საათი\",hh:\"%d საათის\",d:\"დღეს\",dd:\"%d დღის განმავლობაში\",M:\"თვის\",MM:\"%d თვის\",y:\"წელი\",yy:\"%d წლის\"},ordinal:function(_){return _}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ka.js\n"));

/***/ })

}]);