/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_x-pseudo_js"],{

/***/ "./node_modules/dayjs/locale/x-pseudo.js":
/*!***********************************************!*\
  !*** ./node_modules/dayjs/locale/x-pseudo.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=t(e),d={name:\"x-pseudo\",weekdays:\"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý\".split(\"_\"),months:\"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér\".split(\"_\"),weekStart:1,weekdaysShort:\"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát\".split(\"_\"),monthsShort:\"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc\".split(\"_\"),weekdaysMin:\"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"í~ñ %s\",past:\"%s á~gó\",s:\"á ~féw ~sécó~ñds\",m:\"á ~míñ~úté\",mm:\"%d m~íñú~tés\",h:\"á~ñ hó~úr\",hh:\"%d h~óúrs\",d:\"á ~dáý\",dd:\"%d d~áýs\",M:\"á ~móñ~th\",MM:\"%d m~óñt~hs\",y:\"á ~ýéár\",yy:\"%d ý~éárs\"}};return _.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/x-pseudo.js\n"));

/***/ })

}]);