/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ne_js"],{

/***/ "./node_modules/dayjs/locale/ne.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ne.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,_){ true?module.exports=_(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"ne\",weekdays:\"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार\".split(\"_\"),weekdaysShort:\"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.\".split(\"_\"),weekdaysMin:\"आ._सो._मं._बु._बि._शु._श.\".split(\"_\"),months:\"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मे_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर\".split(\"_\"),monthsShort:\"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.\".split(\"_\"),relativeTime:{future:\"%s पछि\",past:\"%s अघि\",s:\"सेकेन्ड\",m:\"एक मिनेट\",mm:\"%d मिनेट\",h:\"घन्टा\",hh:\"%d घन्टा\",d:\"एक दिन\",dd:\"%d दिन\",M:\"एक महिना\",MM:\"%d महिना\",y:\"एक वर्ष\",yy:\"%d वर्ष\"},ordinal:function(e){return(\"\"+e).replace(/\\d/g,(function(e){return\"०१२३४५६७८९\"[e]}))},formats:{LT:\"Aको h:mm बजे\",LTS:\"Aको h:mm:ss बजे\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, Aको h:mm बजे\",LLLL:\"dddd, D MMMM YYYY, Aको h:mm बजे\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ne.js\n"));

/***/ })

}]);