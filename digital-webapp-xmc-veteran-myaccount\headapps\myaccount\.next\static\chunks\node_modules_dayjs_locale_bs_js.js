/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_bs_js"],{

/***/ "./node_modules/dayjs/locale/bs.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/bs.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=t(e),a={name:\"bs\",weekdays:\"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota\".split(\"_\"),months:\"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar\".split(\"_\"),weekStart:1,weekdaysShort:\"ned._pon._uto._sri._čet._pet._sub.\".split(\"_\"),monthsShort:\"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.\".split(\"_\"),weekdaysMin:\"ne_po_ut_sr_če_pe_su\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd, D. MMMM YYYY H:mm\"}};return _.default.locale(a,null,!0),a}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL2JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyxrYUFBa2EsU0FBUyxVQUFVLGtIQUFrSCxxQ0FBcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2RheWpzL2xvY2FsZS9icy5qcz9mYjliIl0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihlLHQpe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPXQocmVxdWlyZShcImRheWpzXCIpKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKFtcImRheWpzXCJdLHQpOihlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuZGF5anNfbG9jYWxlX2JzPXQoZS5kYXlqcyl9KHRoaXMsKGZ1bmN0aW9uKGUpe1widXNlIHN0cmljdFwiO2Z1bmN0aW9uIHQoZSl7cmV0dXJuIGUmJlwib2JqZWN0XCI9PXR5cGVvZiBlJiZcImRlZmF1bHRcImluIGU/ZTp7ZGVmYXVsdDplfX12YXIgXz10KGUpLGE9e25hbWU6XCJic1wiLHdlZWtkYXlzOlwibmVkamVsamFfcG9uZWRqZWxqYWtfdXRvcmFrX3NyaWplZGFfxI1ldHZydGFrX3BldGFrX3N1Ym90YVwiLnNwbGl0KFwiX1wiKSxtb250aHM6XCJqYW51YXJfZmVicnVhcl9tYXJ0X2FwcmlsX21hal9qdW5pX2p1bGlfYXVndXN0X3NlcHRlbWJhcl9va3RvYmFyX25vdmVtYmFyX2RlY2VtYmFyXCIuc3BsaXQoXCJfXCIpLHdlZWtTdGFydDoxLHdlZWtkYXlzU2hvcnQ6XCJuZWQuX3Bvbi5fdXRvLl9zcmkuX8SNZXQuX3BldC5fc3ViLlwiLnNwbGl0KFwiX1wiKSxtb250aHNTaG9ydDpcImphbi5fZmViLl9tYXIuX2Fwci5fbWFqLl9qdW4uX2p1bC5fYXVnLl9zZXAuX29rdC5fbm92Ll9kZWMuXCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzTWluOlwibmVfcG9fdXRfc3JfxI1lX3BlX3N1XCIuc3BsaXQoXCJfXCIpLG9yZGluYWw6ZnVuY3Rpb24oZSl7cmV0dXJuIGV9LGZvcm1hdHM6e0xUOlwiSDptbVwiLExUUzpcIkg6bW06c3NcIixMOlwiREQuTU0uWVlZWVwiLExMOlwiRC4gTU1NTSBZWVlZXCIsTExMOlwiRC4gTU1NTSBZWVlZIEg6bW1cIixMTExMOlwiZGRkZCwgRC4gTU1NTSBZWVlZIEg6bW1cIn19O3JldHVybiBfLmRlZmF1bHQubG9jYWxlKGEsbnVsbCwhMCksYX0pKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/bs.js\n"));

/***/ })

}]);