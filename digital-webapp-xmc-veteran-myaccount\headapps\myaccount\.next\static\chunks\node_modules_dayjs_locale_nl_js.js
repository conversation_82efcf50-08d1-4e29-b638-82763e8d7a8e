/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_nl_js"],{

/***/ "./node_modules/dayjs/locale/nl.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/nl.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,a){ true?module.exports=a(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var d=a(e),n={name:\"nl\",weekdays:\"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag\".split(\"_\"),weekdaysShort:\"zo._ma._di._wo._do._vr._za.\".split(\"_\"),weekdaysMin:\"zo_ma_di_wo_do_vr_za\".split(\"_\"),months:\"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec\".split(\"_\"),ordinal:function(e){return\"[\"+e+(1===e||8===e||e>=20?\"ste\":\"de\")+\"]\"},weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD-MM-YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"over %s\",past:\"%s geleden\",s:\"een paar seconden\",m:\"een minuut\",mm:\"%d minuten\",h:\"een uur\",hh:\"%d uur\",d:\"een dag\",dd:\"%d dagen\",M:\"een maand\",MM:\"%d maanden\",y:\"een jaar\",yy:\"%d jaar\"}};return d.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/nl.js\n"));

/***/ })

}]);