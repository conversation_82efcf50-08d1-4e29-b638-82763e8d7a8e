/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_gl_js"],{

/***/ "./node_modules/dayjs/locale/gl.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/gl.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,o){ true?module.exports=o(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var s=o(e),d={name:\"gl\",weekdays:\"domingo_luns_martes_mércores_xoves_venres_sábado\".split(\"_\"),months:\"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro\".split(\"_\"),weekStart:1,weekdaysShort:\"dom._lun._mar._mér._xov._ven._sáb.\".split(\"_\"),monthsShort:\"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.\".split(\"_\"),weekdaysMin:\"do_lu_ma_mé_xo_ve_sá\".split(\"_\"),ordinal:function(e){return e+\"º\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY H:mm\",LLLL:\"dddd, D [de] MMMM [de] YYYY H:mm\"},relativeTime:{future:\"en %s\",past:\"fai %s\",s:\"uns segundos\",m:\"un minuto\",mm:\"%d minutos\",h:\"unha hora\",hh:\"%d horas\",d:\"un día\",dd:\"%d días\",M:\"un mes\",MM:\"%d meses\",y:\"un ano\",yy:\"%d anos\"}};return s.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/gl.js\n"));

/***/ })

}]);