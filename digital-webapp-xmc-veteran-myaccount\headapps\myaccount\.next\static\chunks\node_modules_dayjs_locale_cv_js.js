/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_cv_js"],{

/***/ "./node_modules/dayjs/locale/cv.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/cv.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),n={name:\"cv\",weekdays:\"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун\".split(\"_\"),months:\"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав\".split(\"_\"),weekStart:1,weekdaysShort:\"выр_тун_ытл_юн_кӗҫ_эрн_шӑм\".split(\"_\"),monthsShort:\"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш\".split(\"_\"),weekdaysMin:\"вр_тн_ыт_юн_кҫ_эр_шм\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD-MM-YYYY\",LL:\"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]\",LLL:\"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm\",LLLL:\"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm\"}};return t.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/cv.js\n"));

/***/ })

}]);