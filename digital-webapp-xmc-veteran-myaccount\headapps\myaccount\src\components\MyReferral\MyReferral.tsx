import {
  Field,
  Image,
  ImageField,
  Link,
  LinkField,
  RichText,
  Text,
  withDatasourceCheck,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { Checkbox, Loader, Modal, Textarea, TextInput } from '@mantine/core';
import Button from 'components/Elements/Button/Button';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { faEnvelope, faUserPlus } from '@fortawesome/pro-solid-svg-icons';
import { faFacebook, faTwitter } from '@fortawesome/free-brands-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useAppSelector } from 'src/stores/store';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { SendReferralEntry, SendReferralInviteBody } from 'src/services/FreeEnergyAPI/types';
import axios from 'axios-1.4';
import { useQuery } from '@tanstack/react-query';
import { CustomerDataResponse } from 'src/services/MyAccountAPI/types';
import { GetUserProfileResponse } from 'src/services/AuthenticationAPI/types';
import { clearCookies } from 'src/utils/clearCookie';

type MyReferralProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    Description: Field<string>;
    ReferralLinkTitle: Field<string>;
    ReferralLink: LinkField;
    Image: ImageField;
    CopyMyReferralLinkText: Field<string>;
    FAQLinkText: Field<string>;
    FAQLink: LinkField;
    FacebookLinkText: Field<string>;
    TwitterLinkText: Field<string>;
    EmailLinkText: Field<string>;
    FacebookIcon: ImageField;
    TwitterIcon: ImageField;
    EmailIcon: ImageField;
    FacebookLink: LinkField;
    TwitterLink: LinkField;
    EmailLink: LinkField;
    LogoutLink: LinkField;
    TitleLabel: Field<string>;
    InviteLabel: Field<string>;
    Name: Field<string>;
    LoginEmail: Field<string>;
    AddNewButton: Field<string>;
    EmailCopy: Field<string>;
    SendButton: Field<string>;
    CancelButton: Field<string>;
    EnterFirstNumberText: Field<string>;
    EnterEmailAddress: Field<string>;
    MaxCountOfReferral: Field<number>;
    SubjectText: Field<string>;
    EmailBodyText: Field<string>;
    InviteSuccessText: Field<string>;
    InviteErrorText: Field<string>;
    RedirectUrlText: Field<string>;
    PersonalWebsiteLinkValue: LinkField;
    CopyReferralLink: LinkField;
    CopiedText: Field<string>;
    IsSignout: Boolean;
  };
};

const MyReferral = (props: MyReferralProps): JSX.Element => {
  const router = useRouter();
  const [inviteMessage, setInviteMessage] = useState<string>('');
  const [sendCopy, setSendCopy] = useState(false);

  const [entries, setEntries] = useState<SendReferralEntry[]>([{ name: '', loginEmail: '' }]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [personalMessage, setPersonalMessage] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const MAX_COUNT = props.fields.MaxCountOfReferral.value;
  const bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
  const [aNumber, setANumber] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState(true);
  const [userProfileData, setUserProfileData] = useState<GetUserProfileResponse>();
  const [copyButton, setCopyButton] = useState(false);

  const selectedAccount = useAppSelector(
    (state) => state?.authuser?.accountSelection?.contractAccount?.value
  );
  const selectedEsiid = useAppSelector((state) => state?.authuser?.accountSelection?.esiid?.value);

  const { data: customerData } = useQuery({
    queryKey: ['getCustomerData', bpNumber, selectedAccount, selectedEsiid],
    queryFn: () =>
      axios
        .get<CustomerDataResponse>('/api/myaccount/customer', {
          params: {
            partnerNumber: bpNumber,
            accountNumber: selectedAccount,
            esiid: selectedEsiid,
          },
        })
        .then((res) => res.data),
    enabled: !!bpNumber && !!selectedAccount && !!selectedEsiid,
  });

  useEffect(() => {
    async function getUserProfileInfo() {
      const req = await axios
        .get<GetUserProfileResponse>('/api/userprofile')
        .then((res) => res.data);

      if (req) {
        if (req?.result?.isFraud) {
          router.push('/oops-payment');
        }
        setUserProfileData(req);
      }
    }
    getUserProfileInfo();
  }, []);

  useEffect(() => {
    if (customerData && selectedAccount) {
      const value = customerData.result?.aNumber || selectedAccount;
      setANumber(value);
    }
  }, [customerData, selectedAccount]);

  const ReferralLinkUpdated = props?.fields?.CopyReferralLink?.value?.href?.replace(
    '{refIdNumber}',
    aNumber ?? ''
  );

  async function logout() {
    console.log('logout');
    clearCookies();
    localStorage.clear();
    sessionStorage.clear();
    const reditrectUrl = props?.fields?.LogoutLink?.value?.href
      ? props?.fields?.LogoutLink?.value?.href
      : '/login';
    router.push(reditrectUrl as string);
  }

  async function copyContentToClipboard() {
    await navigator.clipboard.writeText(ReferralLinkUpdated ?? '');
    setCopyButton(true);
    if (props?.fields?.IsSignout) {
      logout();
    }
  }

  type SocialType = 'fb' | 'tw' | 'em';

  const handleSocialRedirect = (type: SocialType) => {
    const links: Record<SocialType, string | undefined> = {
      fb: props?.fields?.FacebookLink?.value?.href,
      tw: props?.fields?.TwitterLink?.value?.href,
      em: props?.fields?.EmailLink?.value?.href,
    };

    const url = links[type];

    if (url) {
      window.open(url, '_blank');
    }
  };

  const openModalForEmail = () => {
    setInviteMessage('');
    setSendCopy(false);
    setEntries([{ name: '', loginEmail: '' }]);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setInviteMessage('');
    setPersonalMessage('');
    setSendCopy(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const { name, value } = e.target;
    const newEntries = [...entries];
    newEntries[index][name as keyof SendReferralEntry] = value;
    setEntries(newEntries);
  };

  const handleAddFields = () => {
    if (entries.length <= MAX_COUNT) {
      setEntries([...entries, { name: '', loginEmail: '' }]);
    }
  };

  const handleCheckboxChange = () => {
    setSendCopy((prev) => !prev);
  };

  const getOrdinalSuffix = (number: number) => {
    if (number === 1) return '1st';
    if (number === 2) return '2nd';
    if (number === 3) return '3rd';
    return `${number}th`;
  };

  const handleSendInvites = async () => {
    setLoading(true);
    setInviteMessage('');
    try {
      const data: SendReferralInviteBody = {
        entries: entries,
        subject: props?.fields?.SubjectText?.value,
        text: props?.fields?.EmailBodyText?.value,
        redirectUrl: props?.fields?.RedirectUrlText?.value,
        personalMessage,
        sendCopy,
        bpNumber: bpNumber ?? '',
        accountNumber: selectedAccount ?? '',
        webUrl: ReferralLinkUpdated ?? '',
        fullName: userProfileData?.result?.fullName ?? '',
      };
      const response = await axios.post<string>('/api/freeenergy/sendinvite', data);
      console.log('Response from server:', response);
      setInviteMessage(props.fields.InviteSuccessText.value);
      closeModal();
    } catch (err) {
      console.error('Error sending invites:', err);
      setInviteMessage(props.fields.InviteErrorText.value);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="free-rewards flex flex-col gap-5 w-full sm:w-[624px] sm:pl-0 p-5 pt-0 sm:mt-10 wide:ml-0 ipad:pl-6 wide:pl-6 sm:ml-[420px] ipad:ml-0">
      <div className="font-primaryBold text-[18px] text-textUndenary sm:mt-0 mt-10">
        <Text field={props?.fields?.Title} />
      </div>

      {imageLoading && (
        <div className="w-full flex items-center justify-center text-center">
          <Loader />
        </div>
      )}
      <Image
        alt="NoImage"
        field={props?.fields?.Image}
        className="max-w-[226px] md:max-w-[282px]"
        onLoad={() => setImageLoading(false)}
      />

      <div className="font-primaryRegular text-[14px] text-textUndenary">
        <RichText field={props?.fields?.Description} />
      </div>

      {props?.fields?.ReferralLinkTitle && (
        <div className="pt-2 font-primaryBold text-[16px] text-textUndenary">
          <Text field={props?.fields?.ReferralLinkTitle} />
        </div>
      )}

      <div className="w-full">
        <Button
          className="w-full sm:w-auto my-3"
          type="button"
          variant={copyButton ? 'primary' : 'secondary'}
          onClick={copyContentToClipboard}
        >
          {copyButton
            ? props?.fields?.CopiedText?.value
            : props?.fields?.CopyMyReferralLinkText?.value}
        </Button>
      </div>

      <hr className="my-5 border-borderUnvigenary" />

      <div className="w-full">
        {props?.fields?.FacebookLink?.value?.href && (
          <Button
            className="w-full my-3 sm:w-[300px] bg-textTredenary justify-normal text-[16px]"
            type="button"
            variant="primary"
            onClick={() => handleSocialRedirect('fb')}
          >
            <FontAwesomeIcon
              icon={faFacebook}
              className="max-w-[50px] md:max-w-[50px] h-[30px]"
            ></FontAwesomeIcon>

            {props?.fields?.FacebookLinkText?.value}
          </Button>
        )}

        {props?.fields?.TwitterLink?.value?.href && (
          <Button
            className="w-full sm:w-[300px] bg-bgUndenary justify-normal text-[16px] my-[30px]"
            type="button"
            variant="primary"
            onClick={() => handleSocialRedirect('tw')}
          >
            <FontAwesomeIcon
              icon={faTwitter}
              className="max-w-[50px] md:max-w-[50px] h-[30px]"
            ></FontAwesomeIcon>
            {props?.fields?.TwitterLinkText?.value}
          </Button>
        )}

        <Button
          className="w-full my-3 sm:w-[300px] bg-bgVigintioctonary justify-normal text-[16px]"
          type="button"
          variant="primary"
          onClick={openModalForEmail}
        >
          <FontAwesomeIcon
            icon={faEnvelope}
            className="max-w-[50px] md:max-w-[50px] h-[30px]"
          ></FontAwesomeIcon>
          {props?.fields?.EmailLinkText?.value}
        </Button>

        <Modal
          onClose={closeModal}
          opened={isModalOpen}
          size="lg"
          styles={() => {
            return {
              header: {
                paddingLeft: '1.25rem',
                paddingRight: '1.25rem',
                background: '#f3f2f3',
                borderBottom: '0',
              },
              body: {
                marginTop: '24px',
                padding: '24px',
              },
            };
          }}
          title={
            <Text
              tag="h2"
              className="text-textQuattuordenary font-primaryBold text-base my-2"
              field={props.fields.TitleLabel}
            />
          }
        >
          {inviteMessage && (
            <div
              className="absolute top-50 left-0 right-0 p-4 bg-bgQuattuordenary text-textQuinary text-center font-primaryBold rounded-md"
              style={{
                zIndex: 1000, // Ensure it stays on top
                backgroundColor: 'rgba(23, 121, 20, 0.5)',
                backdropFilter: 'blur(5px)',
              }}
              onClick={closeModal} // Close the modal when clicked
            >
              {inviteMessage}
            </div>
          )}
          <div>
            <form>
              {entries.map((entry, index) => (
                <div key={index} className="flex flex-col">
                  <Text
                    className="block text-textQuattuordenary font-primaryRegular my-2"
                    field={{
                      value: `${getOrdinalSuffix(index + 1)} ${props.fields.InviteLabel.value} `,
                    }}
                    tag="p"
                  />

                  <div className="flex flex-col sm:flex-row gap-2 mb-3">
                    <div className="w-full md:w-1/2">
                      <TextInput
                        className="w-full block text-textQuattuordenary font-primaryBold mb-2"
                        id={`firstName-${index}`}
                        label={props.fields.Name.value}
                        name="name"
                        value={entry.name}
                        onChange={(e) => handleChange(e, index)}
                        placeholder={props?.fields?.EnterFirstNumberText?.value}
                      />
                    </div>
                    <div className="w-full md:w-1/2">
                      <TextInput
                        className="w-full block text-textQuattuordenary font-primaryBold mb-2"
                        id={`email-${index}`}
                        label={props.fields.LoginEmail.value}
                        name="loginEmail"
                        value={entry.loginEmail}
                        onChange={(e) => handleChange(e, index)}
                        placeholder={props?.fields?.EnterEmailAddress?.value}
                      />
                    </div>
                  </div>
                </div>
              ))}

              <div className="flex justify-start gap-2 pt-4">
                <Button
                  className="p-0 text-base"
                  disabled={entries.length >= MAX_COUNT}
                  type="button"
                  variant="borderless"
                  onClick={handleAddFields}
                >
                  {props.fields.AddNewButton.value}
                  <FontAwesomeIcon icon={faUserPlus} className="ml-3" />
                </Button>
              </div>

              <div className="flex flex-col gap-4">
                <Textarea
                  className="w-full text-textQuattuordenary font-primaryBold"
                  label="Message"
                  placeholder="Feel free to drop a personal note for everyone invited!"
                  value={personalMessage}
                  onChange={(event) => setPersonalMessage(event.currentTarget.value)}
                  minRows={2}
                  maxRows={4}
                  styles={{
                    label: {
                      marginBottom: '8px',
                    },
                  }}
                />
              </div>

              <div className="flex items-center mt-4">
                <Checkbox
                  checked={sendCopy}
                  className="my-4"
                  id="sendCopy"
                  radius="xs"
                  label={props.fields.EmailCopy.value}
                  onChange={handleCheckboxChange}
                  styles={() => {
                    return {
                      label: {
                        fontFamily: 'OpenSans-Regular',
                        fontSize: '16px',
                        color: '#383543',
                        marginBottom: '8px',
                      },
                      input: {
                        ':checked': {
                          backgroundColor: '#5E32F7',
                          borderColor: '#5E32F7',
                        },
                      },
                    };
                  }}
                />
              </div>

              <div className="flex justify-start gap-2 pt-4">
                <Button
                  type="button"
                  variant="primary"
                  onClick={handleSendInvites}
                  disabled={loading}
                >
                  {loading ? 'Sending Invites...' : props.fields.SendButton.value}
                </Button>
                <Button type="button" variant="secondary" onClick={closeModal}>
                  {props.fields.CancelButton.value}
                </Button>
              </div>
            </form>
          </div>
        </Modal>
      </div>

      <hr className="my-7 border-borderUnvigenary" />

      <div className="font-primaryRegular hover:text-textPrimary text-[16px] mb-10 cursor-pointer text-textSecondary w-fit">
        <Link field={props?.fields?.FAQLink} target="_blank" rel="noopener noreferrer">
          {props?.fields?.FAQLinkText?.value}
        </Link>
      </div>
    </div>
  );
};

export { MyReferral };
const Component = withDatasourceCheck()<MyReferralProps>(MyReferral);
export default aiLogger(Component, Component.name);
