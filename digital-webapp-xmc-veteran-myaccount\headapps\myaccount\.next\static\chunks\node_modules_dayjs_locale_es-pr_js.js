/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_es-pr_js"],{

/***/ "./node_modules/dayjs/locale/es-pr.js":
/*!********************************************!*\
  !*** ./node_modules/dayjs/locale/es-pr.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,o){ true?module.exports=o(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var s=o(e),d={name:\"es-pr\",monthsShort:\"ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic\".split(\"_\"),weekdays:\"domingo_lunes_martes_miércoles_jueves_viernes_sábado\".split(\"_\"),weekdaysShort:\"dom._lun._mar._mié._jue._vie._sáb.\".split(\"_\"),weekdaysMin:\"do_lu_ma_mi_ju_vi_sá\".split(\"_\"),months:\"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre\".split(\"_\"),weekStart:1,formats:{LT:\"h:mm A\",LTS:\"h:mm:ss A\",L:\"MM/DD/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY h:mm A\",LLLL:\"dddd, D [de] MMMM [de] YYYY h:mm A\"},relativeTime:{future:\"en %s\",past:\"hace %s\",s:\"unos segundos\",m:\"un minuto\",mm:\"%d minutos\",h:\"una hora\",hh:\"%d horas\",d:\"un día\",dd:\"%d días\",M:\"un mes\",MM:\"%d meses\",y:\"un año\",yy:\"%d años\"},ordinal:function(e){return e+\"º\"}};return s.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/es-pr.js\n"));

/***/ })

}]);