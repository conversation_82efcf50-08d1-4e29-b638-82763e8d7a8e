/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_nn_js"],{

/***/ "./node_modules/dayjs/locale/nn.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/nn.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=t(e),a={name:\"nn\",weekdays:\"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag\".split(\"_\"),weekdaysShort:\"sun_mån_tys_ons_tor_fre_lau\".split(\"_\"),weekdaysMin:\"su_må_ty_on_to_fr_la\".split(\"_\"),months:\"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,relativeTime:{future:\"om %s\",past:\"for %s sidan\",s:\"nokre sekund\",m:\"eitt minutt\",mm:\"%d minutt\",h:\"ein time\",hh:\"%d timar\",d:\"ein dag\",dd:\"%d dagar\",M:\"ein månad\",MM:\"%d månadar\",y:\"eitt år\",yy:\"%d år\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY [kl.] H:mm\",LLLL:\"dddd D. MMMM YYYY [kl.] HH:mm\"}};return n.default.locale(a,null,!0),a}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/nn.js\n"));

/***/ })

}]);