/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_az_js"],{

/***/ "./node_modules/dayjs/locale/az.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/az.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(a,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var _=e(a),t={name:\"az\",weekdays:\"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə\".split(\"_\"),weekdaysShort:\"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən\".split(\"_\"),weekdaysMin:\"Bz_BE_ÇA_Çə_CA_Cü_Şə\".split(\"_\"),months:\"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr\".split(\"_\"),monthsShort:\"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY г.\",LLL:\"D MMMM YYYY г., H:mm\",LLLL:\"dddd, D MMMM YYYY г., H:mm\"},relativeTime:{future:\"%s sonra\",past:\"%s əvvəl\",s:\"bir neçə saniyə\",m:\"bir dəqiqə\",mm:\"%d dəqiqə\",h:\"bir saat\",hh:\"%d saat\",d:\"bir gün\",dd:\"%d gün\",M:\"bir ay\",MM:\"%d ay\",y:\"bir il\",yy:\"%d il\"},ordinal:function(a){return a}};return _.default.locale(t,null,!0),t}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/az.js\n"));

/***/ })

}]);