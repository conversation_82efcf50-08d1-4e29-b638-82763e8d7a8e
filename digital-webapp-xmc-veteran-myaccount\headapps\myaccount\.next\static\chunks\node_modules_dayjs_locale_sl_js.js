/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_sl_js"],{

/***/ "./node_modules/dayjs/locale/sl.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/sl.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,n){ true?module.exports=n(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e);function r(e){return e%100==2}function a(e){return e%100==3||e%100==4}function s(e,n,t,s){var m=e+\" \";switch(t){case\"s\":return n||s?\"nekaj sekund\":\"nekaj sekundami\";case\"m\":return n?\"ena minuta\":\"eno minuto\";case\"mm\":return r(e)?m+(n||s?\"minuti\":\"minutama\"):a(e)?m+(n||s?\"minute\":\"minutami\"):m+(n||s?\"minut\":\"minutami\");case\"h\":return n?\"ena ura\":\"eno uro\";case\"hh\":return r(e)?m+(n||s?\"uri\":\"urama\"):a(e)?m+(n||s?\"ure\":\"urami\"):m+(n||s?\"ur\":\"urami\");case\"d\":return n||s?\"en dan\":\"enim dnem\";case\"dd\":return r(e)?m+(n||s?\"dneva\":\"dnevoma\"):m+(n||s?\"dni\":\"dnevi\");case\"M\":return n||s?\"en mesec\":\"enim mesecem\";case\"MM\":return r(e)?m+(n||s?\"meseca\":\"mesecema\"):a(e)?m+(n||s?\"mesece\":\"meseci\"):m+(n||s?\"mesecev\":\"meseci\");case\"y\":return n||s?\"eno leto\":\"enim letom\";case\"yy\":return r(e)?m+(n||s?\"leti\":\"letoma\"):a(e)?m+(n||s?\"leta\":\"leti\"):m+(n||s?\"let\":\"leti\")}}var m={name:\"sl\",weekdays:\"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota\".split(\"_\"),months:\"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december\".split(\"_\"),weekStart:1,weekdaysShort:\"ned._pon._tor._sre._čet._pet._sob.\".split(\"_\"),monthsShort:\"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.\".split(\"_\"),weekdaysMin:\"ne_po_to_sr_če_pe_so\".split(\"_\"),ordinal:function(e){return e+\".\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd, D. MMMM YYYY H:mm\",l:\"D. M. YYYY\"},relativeTime:{future:\"čez %s\",past:\"pred %s\",s:s,m:s,mm:s,h:s,hh:s,d:s,dd:s,M:s,MM:s,y:s,yy:s}};return t.default.locale(m,null,!0),m}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/sl.js\n"));

/***/ })

}]);