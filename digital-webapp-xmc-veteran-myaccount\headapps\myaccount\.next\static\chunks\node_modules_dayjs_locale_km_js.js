/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_km_js"],{

/***/ "./node_modules/dayjs/locale/km.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/km.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"km\",weekdays:\"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍\".split(\"_\"),months:\"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ\".split(\"_\"),weekStart:1,weekdaysShort:\"អា_ច_អ_ព_ព្រ_សុ_ស\".split(\"_\"),monthsShort:\"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ\".split(\"_\"),weekdaysMin:\"អា_ច_អ_ព_ព្រ_សុ_ស\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"%sទៀត\",past:\"%sមុន\",s:\"ប៉ុន្មានវិនាទី\",m:\"មួយនាទី\",mm:\"%d នាទី\",h:\"មួយម៉ោង\",hh:\"%d ម៉ោង\",d:\"មួយថ្ងៃ\",dd:\"%d ថ្ងៃ\",M:\"មួយខែ\",MM:\"%d ខែ\",y:\"មួយឆ្នាំ\",yy:\"%d ឆ្នាំ\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/km.js\n"));

/***/ })

}]);