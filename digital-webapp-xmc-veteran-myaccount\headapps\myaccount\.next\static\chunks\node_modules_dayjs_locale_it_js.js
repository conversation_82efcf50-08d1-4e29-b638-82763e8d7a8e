/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_it_js"],{

/***/ "./node_modules/dayjs/locale/it.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/it.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,o){ true?module.exports=o(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=o(e),n={name:\"it\",weekdays:\"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato\".split(\"_\"),weekdaysShort:\"dom_lun_mar_mer_gio_ven_sab\".split(\"_\"),weekdaysMin:\"do_lu_ma_me_gi_ve_sa\".split(\"_\"),months:\"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre\".split(\"_\"),weekStart:1,monthsShort:\"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic\".split(\"_\"),formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"tra %s\",past:\"%s fa\",s:\"qualche secondo\",m:\"un minuto\",mm:\"%d minuti\",h:\"un' ora\",hh:\"%d ore\",d:\"un giorno\",dd:\"%d giorni\",M:\"un mese\",MM:\"%d mesi\",y:\"un anno\",yy:\"%d anni\"},ordinal:function(e){return e+\"º\"}};return t.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/it.js\n"));

/***/ })

}]);