import {
  Text,
  Field,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleMinus, faSpinner } from '@fortawesome/pro-regular-svg-icons';
import { faPen } from '@fortawesome/pro-solid-svg-icons';
import Tooltip from 'components/Elements/Tooltip/Tooltip';
import QuestionCircle from 'assets/icons/QuestionCircle';
import EditableTextForm from 'components/common/EditableTextForm/EditableTextForm';
import { useForm, zodResolver } from '@mantine/form';
import { Checkbox, UnstyledButton } from '@mantine/core';
import {
  CustomerDataResponse,
  UpdateAccountDescriptionBody,
  UpdateAccountDescriptionResponse,
  UpdateBillingAddressBody,
  UpdateBillingAddressResponse,
} from 'src/services/MyAccountAPI/types';
import { useAppSelector } from 'src/stores/store';
import axios, { AxiosResponse } from 'axios-1.4';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import EditableNumberBox from 'components/common/EditableNumberBox/EditableNumberBox';
import { z } from 'zod';
import Button from 'components/Elements/Button/Button';
import EditableSelect from 'components/common/EditableSelect/EditableSelect';
import { useLoader } from 'src/hooks/modalhooks';
import Loader from 'components/common/Loader/Loader';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';
import Pen from 'assets/icons/Pen';
import Question from 'assets/icons/Question';

type AddressDetailsProps = ComponentProps & {
  fields: {
    data: {
      item: {
        heading: Field<string>;
        HouseText: Field<string>;
        ServiceAddressText: Field<string>;
        AccountMailingAddressText: Field<string>;
        EditButtonText: Field<string>;
        CancelButtonText: Field<string>;
        AccountNicknameText: Field<string>;
        StreetAddressText: Field<string>;
        ApartmentNumberText: Field<string>;
        CityText: Field<string>;
        StateText: Field<string>;
        ZipcodeText: Field<string>;
        POBoxText: Field<string>;
        SaveChangesButtonText: Field<string>;
        CancelChangesButtonText: Field<string>;
        MailingAddressState: {
          targetItems: { displayName: string }[];
        };
        MailingAddressTooltip: Field<string>;
        StreetAddressValidationError: Field<string>;
        CityValidationError: Field<string>;
        StateValidationError: Field<string>;
        ZipCodeValidationError: Field<string>;
        POBoxValidationError: Field<string>;
        POCheckBoxLabel: Field<string>;
        ErrorMessage: Field<string>;
      };
    };
  };
  isCustomerDatafetched: boolean;
  customerData: CustomerDataResponse;
};

const AddressDetails = (props: AddressDetailsProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="AddressDetails" />;

  console.log(props, '--');
  const { openModal } = useLoader();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [isPoBoxChecked, setPoBoxCheck] = useState(false);
  const selectedAccount = useAppSelector(
    (state) => state.authuser.accountSelection.contractAccount?.value
  );
  const handlePoBoxChange = () => {
    setPoBoxCheck(isPoBoxChecked ? false : true);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    form.validate();
    if (form.isValid()) {
      mutation.mutate(form.values);
    }
  };

  const mutation = useMutation({
    mutationFn: async (values: Record<string, string>) => {
      const address = values.streetAddress.split(' ');
      await axios.post<
        UpdateAccountDescriptionResponse,
        AxiosResponse<UpdateAccountDescriptionResponse, UpdateAccountDescriptionBody>,
        UpdateAccountDescriptionBody
      >('/api/myaccount/customer/updateAccountNickName', {
        ContractAccount: selectedAccount,
        accountDescription: values.accountNickName,
      });
      return await axios.post<
        UpdateBillingAddressResponse,
        AxiosResponse<UpdateBillingAddressResponse, UpdateBillingAddressBody>,
        UpdateBillingAddressBody
      >('/api/myaccount/customer/updateBillingAddress', {
        ContractAccount: selectedAccount,
        StreetNo: address[0],
        StreetName: address.slice(1).join(' '),
        UnitNumber: values.aptNbr,
        City: values.city,
        State: values.state,
        ZipCode: values.zipCd,
        PoBox: isPoBoxChecked ? values.poBox : '',
        Country: 'US',
      });
    },
    onSuccess: (data) => {
      if (!data.data.hasErrors) {
        queryClient.invalidateQueries({
          queryKey: ['customerData'],
        });
        setIsEditing(false);
        openModal();
        window.location.reload();
      }
    },
  });
  // The below methos is not called anywhere, so comming this as  build issue fix
  // const handleCancelChanges = () => {
  //   form.setValues({
  //     accountNickName: props.customerData?.result?.accountNickname || '',
  //     streetAddress:
  //       (props.customerData?.result?.mailingAddress?.streetNumber || '') +
  //       ' ' +
  //       (props.customerData?.result?.mailingAddress?.streetName || ''),
  //     aptNbr: props.customerData?.result?.mailingAddress?.aptNumber || '',
  //     city: props.customerData?.result?.mailingAddress?.city || '',
  //     state: props.customerData?.result?.mailingAddress?.state || '',
  //     zipCd: parseInt(props.customerData?.result?.mailingAddress?.postalCode.substr(0, 5)) || '',
  //     poBox: parseInt(props.customerData?.result?.mailingAddress?.poBox) || '',
  //   });
  //   props.customerData?.result.mailingAddress.poBox !== ''
  //     ? setPoBoxCheck(true)
  //     : setPoBoxCheck(false);
  // };

  const handleCancel = () => {
    form.setValues({
      accountNickName: props.customerData?.result?.accountNickname || '',
      streetAddress:
        (props.customerData?.result?.mailingAddress?.streetNumber || '') +
        ' ' +
        (props.customerData?.result?.mailingAddress?.streetName || ''),
      aptNbr: props.customerData?.result?.mailingAddress?.aptNumber || '',
      city: props.customerData?.result?.mailingAddress?.city || '',
      state: props.customerData?.result?.mailingAddress?.state || '',
      zipCd: parseInt(props.customerData?.result?.mailingAddress?.postalCode.substr(0, 5)) || '',
      poBox: parseInt(props.customerData?.result?.mailingAddress?.poBox) || '',
    });
    props.customerData?.result.mailingAddress.poBox !== ''
      ? setPoBoxCheck(true)
      : setPoBoxCheck(false);
    setIsEditing(false);
  };

  const UpdateMailingAddressSchema = z.object({
    accountNickName: z.string().optional(),
    streetAddress: isPoBoxChecked
      ? z.string().optional()
      : z
          .string()
          .nonempty({ message: props?.fields?.data?.item?.StreetAddressValidationError.value }),
    aptNbr: z.string().optional(),
    city: z.string().nonempty({ message: props?.fields?.data?.item?.CityValidationError.value }),
    state: z.string().nonempty({ message: props?.fields?.data?.item?.StateValidationError.value }),
    // zipCd: z.coerce.number().min(1, props?.fields?.data?.item?.ZipCodeValidationError.value),
    zipCd: z.coerce
      .number()
      .min(10000, { message: props?.fields?.data?.item?.ZipCodeValidationError.value })
      .max(99999, { message: props?.fields?.data?.item?.ZipCodeValidationError.value }),
    saveAsDefault: z.boolean().optional(),
    poBox: isPoBoxChecked
      ? z.coerce.number().min(1, props?.fields?.data?.item?.POBoxValidationError.value)
      : z.coerce.number().optional(),
  });

  const form = useForm({
    initialValues: {
      accountNickName: '',
      streetAddress: '',
      aptNbr: '',
      city: '',
      state: '',
      zipCd: '',
      poBox: '',
    },
    validate: zodResolver(UpdateMailingAddressSchema),
    validateInputOnChange: true,
    validateInputOnBlur: true,
  });

  useEffect(() => {
    if (props.customerData) {
      form.setValues({
        accountNickName: props.customerData?.result?.accountNickname || '',
        streetAddress:
          props.customerData?.result?.mailingAddress?.streetName !== ''
            ? (props.customerData?.result?.mailingAddress?.streetNumber || '') +
              ' ' +
              (props.customerData?.result?.mailingAddress?.streetName || '')
            : '',
        aptNbr: props.customerData?.result?.mailingAddress?.aptNumber || '',
        city: props.customerData?.result?.mailingAddress?.city || '',
        state: props.customerData?.result?.mailingAddress?.state || '',
        zipCd: parseInt(props.customerData?.result?.mailingAddress?.postalCode.substr(0, 5)) || '',
        poBox: parseInt(props.customerData?.result?.mailingAddress?.poBox) || '',
      });
      props.customerData?.result?.mailingAddress?.poBox !== ''
        ? setPoBoxCheck(true)
        : setPoBoxCheck(false);
    }
  }, [props.customerData]);

  if (props?.customerData?.hasErrors) {
    return (
      <div className="text-textDenary text-2xl">
        {props?.fields?.data?.item?.ErrorMessage?.value}
      </div>
    );
  }
  if (
    props.isCustomerDatafetched &&
    props.customerData &&
    props.customerData.result &&
    props.customerData.result.formattedMailingAddr
  ) {
    return (
      <div>
        <Text field={props?.fields?.data?.item?.heading} />
        <div className="sm:flex sm:flex-row gap-4 justify-between mb-16 max-w-[830px]">
          <div className="sm:m-0 w-full shadow-none rounded-none max-w-[830px]">
            <div className="bg-bgQuattuordenary flex justify-between rounded-t-xl font-primaryBlack items-center bg-transparent p-0">
              <div className="sm:text-plus2 text-base text-textPrimary text-center rounded-t-xl">
                {props.isCustomerDatafetched ? (
                  <>
                    <h2 className="font-primaryBold text-textUndenary">
                      {props.customerData?.result?.accountNickname !== ''
                        ? `${props.customerData.result.accountNickname}: ${selectedAccount}`
                        : `${props?.fields?.data?.item?.HouseText.value} ${selectedAccount}`}
                    </h2>
                  </>
                ) : (
                  <></>
                )}
              </div>
              {isEditing ? (
                <button
                  type="button"
                  className="hidden cursor-pointer items-center text-textPrimary hover:text-textSecondary text-shadow font-primaryBold text-base "
                  onClick={handleCancel}
                >
                  {props?.fields?.data?.item?.CancelButtonText.value}
                  <FontAwesomeIcon
                    icon={faCircleMinus}
                    className="text-minus1 inline-block ml-2 cursor-pointer"
                  />
                </button>
              ) : (
                <button
                  type="button"
                  className="cursor-pointer flex items-center text-textPrimary hover:text-textSecondary font-primaryBold text-base"
                  onClick={handleEdit}
                >
                  <span className="hidden">{props?.fields?.data?.item?.EditButtonText.value}</span>

                  <span className="inline-block ml-2 mb-1 cursor-pointer font-primaryBold text-textPrimary text-base">
                    <Pen />
                  </span>
                </button>
              )}
            </div>

            <div className="p-0">
              <div className="flex sm:flex-row flex-col gap-6 sm:gap-6">
                {isEditing ? (
                  <div className="pt-6 sm:w-[348px] sm:max-w-[348px] max-w-[315px] w-[315px] pb-0 sm:pb-0">
                    <EditableTextForm
                      form={form}
                      formField="accountNickName"
                      label={
                        <span className="text-minus1 sm:text-base">
                          {props?.fields?.data?.item?.AccountNicknameText.value}
                        </span>
                      }
                      error=""
                      defaultvalue={form.values.accountNickName}
                      showInput={isEditing}
                    ></EditableTextForm>
                  </div>
                ) : null}

                <div>
                  <div
                    className={`font-primaryBlack sm:text-base text-minus1 text-textPrimary pt-6 sm:pt-6 ${
                      isEditing ? 'pt-0' : 'pt-6'
                    }`}
                  >
                    <h3 className="font-primaryBold text-textUndenary text-minus1 sm:text-base">
                      {props?.fields?.data?.item?.ServiceAddressText.value}
                    </h3>
                  </div>
                  <div>
                    <hr className="w-full border-[1px] my-1 hidden"></hr>
                    <div className="sm:text-base text-minus1 font-primaryRegular pb-4 pt-2 sm:pt-5 sm:pb-0">
                      <span
                        className={`font-primaryRegular text-textUndenary text-minus1 sm:text-base ${
                          isEditing ? '' : 'text-textQuattuordenary'
                        }  `}
                      >
                        {props?.customerData?.result?.serviceAddress}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="font-primaryBlack sm:text-base text-minus1 text-textPrimary pt-6 hidden">
                <h3 className="font-primaryBold text-textUndenary">
                  {props?.fields?.data?.item?.ServiceAddressText.value}
                </h3>
              </div>

              <div className="hidden">
                <hr className="w-full border-[1px] my-1 hidden"></hr>
                <div className="sm:text-base text-minus1 font-primaryRegular pt-4 pb-4">
                  <span className="text-textQuattuordenary">
                    {props?.customerData?.result?.serviceAddress}
                  </span>
                </div>
              </div>

              <div className="flex font-primaryBlack sm:text-base text-minus1 text-textPrimary sm:pt-6 pt-0">
                <h3 className="font-primaryBold text-textUndenary mb-2">
                  {props?.fields?.data?.item?.AccountMailingAddressText.value}
                </h3>
                <div className="pl-2">
                  <Tooltip
                    content={{ value: props?.fields?.data?.item?.MailingAddressTooltip.value }}
                    className="selected-tooltip address-details-tooltip"
                    arrowclassName="selected-tooltip-icon"
                  >
                    <Question />
                  </Tooltip>
                </div>
              </div>

              <div>
                <hr className="w-full border-[1px] my-1 hidden"></hr>
                {isEditing ? (
                  <div>
                    <div className="mt-2">
                      <Checkbox
                        onChange={handlePoBoxChange}
                        checked={isPoBoxChecked}
                        styles={{
                          body: {
                            display: 'flex',
                            alignItems: 'center',
                          },
                        }}
                        radius="xs"
                        label={props?.fields?.data?.item?.POCheckBoxLabel.value}
                      />
                    </div>

                    <div
                      className={`pt-6 pb-3 flex flex-col sm:flex-row gap-6 ${
                        isPoBoxChecked ? 'gap-0' : 'gap-6'
                      }`}
                    >
                      <div className="w-[315px] max-w-[315px] sm:w-[348px] sm:max-w-[348px]">
                        {isPoBoxChecked ? (
                          <div className="col-start-4 col-end-4">
                            <EditableNumberBox
                              form={form}
                              formField="poBox"
                              label={
                                <span className="text-minus1 sm:text-base">
                                  {props?.fields?.data?.item?.POBoxText.value}
                                </span>
                              }
                              error=""
                              defaultvalue={form.values.poBox}
                              showInput={isEditing}
                            ></EditableNumberBox>
                          </div>
                        ) : (
                          <EditableTextForm
                            form={form}
                            formField="streetAddress"
                            label={
                              <span className="text-minus1 sm:text-base">
                                {props?.fields?.data?.item?.StreetAddressText.value}
                              </span>
                            }
                            error=""
                            defaultvalue={form.values.streetAddress}
                            showInput={isEditing}
                          ></EditableTextForm>
                        )}
                      </div>
                      <div className="w-[315px] max-w-[315px] sm:w-[348px] sm:max-w-[348px]">
                        {isPoBoxChecked ? (
                          <></>
                        ) : (
                          <EditableTextForm
                            form={form}
                            formField="aptNbr"
                            label={
                              <span className="text-minus1 sm:text-base">
                                {props?.fields?.data?.item?.ApartmentNumberText.value}
                              </span>
                            }
                            error=""
                            defaultvalue={form.values.aptNbr}
                            showInput={isEditing}
                          ></EditableTextForm>
                        )}
                      </div>
                    </div>

                    <div className="pt-2 flex flex-wrap gap-6 flex-col sm:flex-row sm:pb-6 pb-6">
                      <div className="w-[315px] max-w-[315px] sm:w-[348px] sm:max-w-[348px]">
                        <EditableTextForm
                          form={form}
                          formField="city"
                          label={
                            <span className="text-minus1 sm:text-base">
                              {props?.fields?.data?.item?.CityText.value}
                            </span>
                          }
                          error=""
                          defaultvalue={form.values.city}
                          showInput={isEditing}
                        ></EditableTextForm>
                      </div>
                      <div className="basis-[45%] w-[315px] max-w-[315px] sm:w-[348px] sm:max-w-[348px] ">
                        <EditableSelect
                          form={form}
                          formField="state"
                          label={
                            <span className="text-minus1 sm:text-base">
                              {props?.fields?.data?.item?.StateText.value}
                            </span>
                          }
                          error=""
                          defaultvalue={form.values.state}
                          showInput={isEditing}
                          data={props?.fields?.data?.item?.MailingAddressState?.targetItems?.map(
                            (state) => state.displayName
                          )}
                        ></EditableSelect>
                      </div>
                    </div>

                    <div className="basis-[45%] w-[315px] max-w-[315px] sm:w-[348px] sm:max-w-[348px]">
                      <EditableNumberBox
                        form={form}
                        formField="zipCd"
                        label={
                          <span className="text-minus1 sm:text-base">
                            {props?.fields?.data?.item?.ZipcodeText.value}
                          </span>
                        }
                        error=""
                        defaultvalue={form.values.zipCd}
                        showInput={isEditing}
                      ></EditableNumberBox>
                    </div>
                  </div>
                ) : (
                  <div className="sm:text-base text-minus1 font-primaryRegular pb-5 pt-2">
                    <span className="text-textQuattuordenary">
                      {props.customerData?.result?.mailingAddress?.poBox !== ''
                        ? `${props?.fields?.data?.item?.POCheckBoxLabel.value} ${props.customerData?.result?.formattedMailingAddr}`
                        : props.customerData?.result?.formattedMailingAddr}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {isEditing ? (
              <div className="flex sm:flex-row flex-col items-center justify-center sm:justify-start gap-6 p-8 px-0">
                <Button
                  icon={
                    mutation.isLoading ?? (
                      <FontAwesomeIcon
                        icon={faSpinner}
                        className="text-textQuinary"
                        size="xs"
                        spin
                      />
                    )
                  }
                  disabled={mutation.isLoading || !form.isValid()}
                  variant="primary"
                  className="sm:px-6 sm:text-textQuinary w-full sm:w-auto py-[14px] text-[18px]"
                  onClick={handleSave}
                >
                  {props?.fields?.data?.item?.SaveChangesButtonText?.value}
                </Button>

                {/* cancel button for ambit */}
                <Button
                  variant="secondary"
                  className="block w-full sm:w-auto"
                  onClick={handleCancel}
                >
                  {props?.fields?.data?.item?.CancelChangesButtonText?.value}
                </Button>
                {/* cancel button for ambit*/}

                <UnstyledButton
                  className="hidden text-textPrimary hover:text-textSecondary font-primaryBlack text-base sm:w-auto sm:max-w-[none] max-h-[none]"
                  onClick={handleCancel}
                >
                  {props?.fields?.data?.item?.CancelChangesButtonText?.value}
                  <FontAwesomeIcon
                    icon={faCircleMinus}
                    className="text-minus1 ml-2 cursor-pointer hidden"
                  />
                </UnstyledButton>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div>
        <div className="flex flex-col max-w-[656px] h-80 md:h-64 bg-[transparent]">
          <Loader />
        </div>
      </div>
    );
  }
};

export { AddressDetails };
const Component = withDatasourceCheck()<AddressDetailsProps>(AddressDetails);
export default aiLogger(Component, Component.name);
