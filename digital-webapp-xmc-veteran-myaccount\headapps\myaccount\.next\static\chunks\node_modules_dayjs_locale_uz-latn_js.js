/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_uz-latn_js"],{

/***/ "./node_modules/dayjs/locale/uz-latn.js":
/*!**********************************************!*\
  !*** ./node_modules/dayjs/locale/uz-latn.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(a,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var _=e(a),n={name:\"uz-latn\",weekdays:\"Yakshanba_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba\".split(\"_\"),months:\"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr\".split(\"_\"),weekStart:1,weekdaysShort:\"Yak_Dush_Sesh_Chor_Pay_Jum_Shan\".split(\"_\"),monthsShort:\"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek\".split(\"_\"),weekdaysMin:\"Ya_Du_Se_Cho_Pa_Ju_Sha\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"D MMMM YYYY, dddd HH:mm\"},relativeTime:{future:\"Yaqin %s ichida\",past:\"%s oldin\",s:\"soniya\",m:\"bir daqiqa\",mm:\"%d daqiqa\",h:\"bir soat\",hh:\"%d soat\",d:\"bir kun\",dd:\"%d kun\",M:\"bir oy\",MM:\"%d oy\",y:\"bir yil\",yy:\"%d yil\"}};return _.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/uz-latn.js\n"));

/***/ })

}]);