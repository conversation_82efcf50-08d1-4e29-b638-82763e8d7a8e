/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ta_js"],{

/***/ "./node_modules/dayjs/locale/ta.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ta.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ta\",weekdays:\"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை\".split(\"_\"),months:\"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்\".split(\"_\"),weekdaysShort:\"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி\".split(\"_\"),monthsShort:\"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்\".split(\"_\"),weekdaysMin:\"ஞா_தி_செ_பு_வி_வெ_ச\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, HH:mm\",LLLL:\"dddd, D MMMM YYYY, HH:mm\"},relativeTime:{future:\"%s இல்\",past:\"%s முன்\",s:\"ஒரு சில விநாடிகள்\",m:\"ஒரு நிமிடம்\",mm:\"%d நிமிடங்கள்\",h:\"ஒரு மணி நேரம்\",hh:\"%d மணி நேரம்\",d:\"ஒரு நாள்\",dd:\"%d நாட்கள்\",M:\"ஒரு மாதம்\",MM:\"%d மாதங்கள்\",y:\"ஒரு வருடம்\",yy:\"%d ஆண்டுகள்\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ta.js\n"));

/***/ })

}]);