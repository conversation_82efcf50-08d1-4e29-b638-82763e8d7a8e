/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ml_js"],{

/***/ "./node_modules/dayjs/locale/ml.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ml.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ml\",weekdays:\"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച\".split(\"_\"),months:\"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ\".split(\"_\"),weekdaysShort:\"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി\".split(\"_\"),monthsShort:\"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.\".split(\"_\"),weekdaysMin:\"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm -നു\",LTS:\"A h:mm:ss -നു\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm -നു\",LLLL:\"dddd, D MMMM YYYY, A h:mm -നു\"},relativeTime:{future:\"%s കഴിഞ്ഞ്\",past:\"%s മുൻപ്\",s:\"അൽപ നിമിഷങ്ങൾ\",m:\"ഒരു മിനിറ്റ്\",mm:\"%d മിനിറ്റ്\",h:\"ഒരു മണിക്കൂർ\",hh:\"%d മണിക്കൂർ\",d:\"ഒരു ദിവസം\",dd:\"%d ദിവസം\",M:\"ഒരു മാസം\",MM:\"%d മാസം\",y:\"ഒരു വർഷം\",yy:\"%d വർഷം\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL21sLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyx3YkFBd2IsU0FBUyxVQUFVLHVJQUF1SSxlQUFlLDRNQUE0TSxxQ0FBcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2RheWpzL2xvY2FsZS9tbC5qcz81NjBlIl0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihfLGUpe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPWUocmVxdWlyZShcImRheWpzXCIpKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKFtcImRheWpzXCJdLGUpOihfPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6X3x8c2VsZikuZGF5anNfbG9jYWxlX21sPWUoXy5kYXlqcyl9KHRoaXMsKGZ1bmN0aW9uKF8pe1widXNlIHN0cmljdFwiO2Z1bmN0aW9uIGUoXyl7cmV0dXJuIF8mJlwib2JqZWN0XCI9PXR5cGVvZiBfJiZcImRlZmF1bHRcImluIF8/Xzp7ZGVmYXVsdDpffX12YXIgdD1lKF8pLGQ9e25hbWU6XCJtbFwiLHdlZWtkYXlzOlwi4LSe4LS+4LSv4LSx4LS+4LS04LWN4LSaX+C0pOC0v+C0meC1jeC0leC0s+C0vuC0tOC1jeC0ml/gtJrgtYrgtLXgtY3gtLXgtL7gtLTgtY3gtJpf4LSs4LWB4LSn4LSo4LS+4LS04LWN4LSaX+C0teC1jeC0r+C0vuC0tOC0vuC0tOC1jeC0ml/gtLXgtYbgtLPgtY3gtLPgtL/gtK/gtL7gtLTgtY3gtJpf4LS24LSo4LS/4LSv4LS+4LS04LWN4LSaXCIuc3BsaXQoXCJfXCIpLG1vbnRoczpcIuC0nOC0qOC1geC0teC0sOC0v1/gtKvgtYbgtKzgtY3gtLDgtYHgtLXgtLDgtL9f4LSu4LS+4LW84LSa4LWN4LSa4LWNX+C0j+C0quC1jeC0sOC0v+C1vV/gtK7gtYfgtK/gtY1f4LSc4LWC4LW6X+C0nOC1guC0suC1iF/gtJPgtJfgtLjgtY3gtLHgtY3gtLHgtY1f4LS44LWG4LSq4LWN4LSx4LWN4LSx4LSC4LSs4LW8X+C0kuC0leC1jeC0n+C1i+C0rOC1vF/gtKjgtLXgtILgtKzgtbxf4LSh4LS/4LS44LSC4LSs4LW8XCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzU2hvcnQ6XCLgtJ7gtL7gtK/gtbxf4LSk4LS/4LSZ4LWN4LSV4LW+X+C0muC1iuC0teC1jeC0tV/gtKzgtYHgtKfgtbtf4LS14LWN4LSv4LS+4LS04LSCX+C0teC1huC0s+C1jeC0s+C0v1/gtLbgtKjgtL9cIi5zcGxpdChcIl9cIiksbW9udGhzU2hvcnQ6XCLgtJzgtKjgtYEuX+C0q+C1huC0rOC1jeC0sOC1gS5f4LSu4LS+4LW8Ll/gtI/gtKrgtY3gtLDgtL8uX+C0ruC1h+C0r+C1jV/gtJzgtYLgtbpf4LSc4LWC4LSy4LWILl/gtJPgtJcuX+C0uOC1huC0quC1jeC0seC1jeC0sS5f4LSS4LSV4LWN4LSf4LWLLl/gtKjgtLXgtIIuX+C0oeC0v+C0uOC0gi5cIi5zcGxpdChcIl9cIiksd2Vla2RheXNNaW46XCLgtJ7gtL5f4LSk4LS/X+C0muC1il/gtKzgtYFf4LS14LWN4LSv4LS+X+C0teC1hl/gtLZcIi5zcGxpdChcIl9cIiksb3JkaW5hbDpmdW5jdGlvbihfKXtyZXR1cm4gX30sZm9ybWF0czp7TFQ6XCJBIGg6bW0gLeC0qOC1gVwiLExUUzpcIkEgaDptbTpzcyAt4LSo4LWBXCIsTDpcIkREL01NL1lZWVlcIixMTDpcIkQgTU1NTSBZWVlZXCIsTExMOlwiRCBNTU1NIFlZWVksIEEgaDptbSAt4LSo4LWBXCIsTExMTDpcImRkZGQsIEQgTU1NTSBZWVlZLCBBIGg6bW0gLeC0qOC1gVwifSxyZWxhdGl2ZVRpbWU6e2Z1dHVyZTpcIiVzIOC0leC0tOC0v+C0nuC1jeC0nuC1jVwiLHBhc3Q6XCIlcyDgtK7gtYHgtbvgtKrgtY1cIixzOlwi4LSF4LW94LSqIOC0qOC0v+C0ruC0v+C0t+C0meC1jeC0meC1vlwiLG06XCLgtJLgtLDgtYEg4LSu4LS/4LSo4LS/4LSx4LWN4LSx4LWNXCIsbW06XCIlZCDgtK7gtL/gtKjgtL/gtLHgtY3gtLHgtY1cIixoOlwi4LSS4LSw4LWBIOC0ruC0o+C0v+C0leC1jeC0leC1guC1vFwiLGhoOlwiJWQg4LSu4LSj4LS/4LSV4LWN4LSV4LWC4LW8XCIsZDpcIuC0kuC0sOC1gSDgtKbgtL/gtLXgtLjgtIJcIixkZDpcIiVkIOC0puC0v+C0teC0uOC0glwiLE06XCLgtJLgtLDgtYEg4LSu4LS+4LS44LSCXCIsTU06XCIlZCDgtK7gtL7gtLjgtIJcIix5Olwi4LSS4LSw4LWBIOC0teC1vOC0t+C0glwiLHl5OlwiJWQg4LS14LW84LS34LSCXCJ9fTtyZXR1cm4gdC5kZWZhdWx0LmxvY2FsZShkLG51bGwsITApLGR9KSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ml.js\n"));

/***/ })

}]);