/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_tg_js"],{

/***/ "./node_modules/dayjs/locale/tg.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/tg.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"tg\",weekdays:\"якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе\".split(\"_\"),months:\"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр\".split(\"_\"),weekStart:1,weekdaysShort:\"яшб_дшб_сшб_чшб_пшб_ҷум_шнб\".split(\"_\"),monthsShort:\"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек\".split(\"_\"),weekdaysMin:\"яш_дш_сш_чш_пш_ҷм_шб\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"баъди %s\",past:\"%s пеш\",s:\"якчанд сония\",m:\"як дақиқа\",mm:\"%d дақиқа\",h:\"як соат\",hh:\"%d соат\",d:\"як рӯз\",dd:\"%d рӯз\",M:\"як моҳ\",MM:\"%d моҳ\",y:\"як сол\",yy:\"%d сол\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/tg.js\n"));

/***/ })

}]);