/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_tr_js"],{

/***/ "./node_modules/dayjs/locale/tr.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/tr.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(a,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var t=e(a),_={name:\"tr\",weekdays:\"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi\".split(\"_\"),weekdaysShort:\"Paz_Pts_Sal_Çar_Per_Cum_Cts\".split(\"_\"),weekdaysMin:\"Pz_Pt_Sa_Ça_Pe_Cu_Ct\".split(\"_\"),months:\"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık\".split(\"_\"),monthsShort:\"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara\".split(\"_\"),weekStart:1,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"%s sonra\",past:\"%s önce\",s:\"birkaç saniye\",m:\"bir dakika\",mm:\"%d dakika\",h:\"bir saat\",hh:\"%d saat\",d:\"bir gün\",dd:\"%d gün\",M:\"bir ay\",MM:\"%d ay\",y:\"bir yıl\",yy:\"%d yıl\"},ordinal:function(a){return a+\".\"}};return t.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/tr.js\n"));

/***/ })

}]);