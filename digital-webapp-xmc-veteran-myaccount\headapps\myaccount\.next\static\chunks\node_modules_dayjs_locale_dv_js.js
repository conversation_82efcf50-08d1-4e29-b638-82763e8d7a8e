/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_dv_js"],{

/***/ "./node_modules/dayjs/locale/dv.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/dv.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"dv\",weekdays:\"އާދިއްތަ_ހޯމަ_އަންގާރަ_ބުދަ_ބުރާސްފަތި_ހުކުރު_ހޮނިހިރު\".split(\"_\"),months:\"ޖެނުއަރީ_ފެބްރުއަރީ_މާރިޗު_އޭޕްރީލު_މޭ_ޖޫން_ޖުލައި_އޯގަސްޓު_ސެޕްޓެމްބަރު_އޮކްޓޯބަރު_ނޮވެމްބަރު_ޑިސެމްބަރު\".split(\"_\"),weekStart:7,weekdaysShort:\"އާދިއްތަ_ހޯމަ_އަންގާރަ_ބުދަ_ބުރާސްފަތި_ހުކުރު_ހޮނިހިރު\".split(\"_\"),monthsShort:\"ޖެނުއަރީ_ފެބްރުއަރީ_މާރިޗު_އޭޕްރީލު_މޭ_ޖޫން_ޖުލައި_އޯގަސްޓު_ސެޕްޓެމްބަރު_އޮކްޓޯބަރު_ނޮވެމްބަރު_ޑިސެމްބަރު\".split(\"_\"),weekdaysMin:\"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"D/M/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"ތެރޭގައި %s\",past:\"ކުރިން %s\",s:\"ސިކުންތުކޮޅެއް\",m:\"މިނިޓެއް\",mm:\"މިނިޓު %d\",h:\"ގަޑިއިރެއް\",hh:\"ގަޑިއިރު %d\",d:\"ދުވަހެއް\",dd:\"ދުވަސް %d\",M:\"މަހެއް\",MM:\"މަސް %d\",y:\"އަހަރެއް\",yy:\"އަހަރު %d\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/dv.js\n"));

/***/ })

}]);