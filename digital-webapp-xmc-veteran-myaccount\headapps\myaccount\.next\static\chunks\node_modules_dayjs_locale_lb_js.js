/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_lb_js"],{

/***/ "./node_modules/dayjs/locale/lb.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/lb.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,_){ true?module.exports=_(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),n={name:\"lb\",weekdays:\"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg\".split(\"_\"),months:\"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember\".split(\"_\"),weekStart:1,weekdaysShort:\"So._Mé._Dë._Më._Do._Fr._Sa.\".split(\"_\"),monthsShort:\"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.\".split(\"_\"),weekdaysMin:\"So_Mé_Dë_Më_Do_Fr_Sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"H:mm [Auer]\",LTS:\"H:mm:ss [Auer]\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm [Auer]\",LLLL:\"dddd, D. MMMM YYYY H:mm [Auer]\"}};return t.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL2xiLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBb0Qsa0JBQWtCLG1CQUFPLENBQUMsZ0RBQU8sR0FBRyxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLGNBQWMsK0NBQStDLFdBQVcsY0FBYyxxYUFBcWEsU0FBUyxVQUFVLDhJQUE4SSxxQ0FBcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2RheWpzL2xvY2FsZS9sYi5qcz9jMGFlIl0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihlLF8pe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPV8ocmVxdWlyZShcImRheWpzXCIpKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKFtcImRheWpzXCJdLF8pOihlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuZGF5anNfbG9jYWxlX2xiPV8oZS5kYXlqcyl9KHRoaXMsKGZ1bmN0aW9uKGUpe1widXNlIHN0cmljdFwiO2Z1bmN0aW9uIF8oZSl7cmV0dXJuIGUmJlwib2JqZWN0XCI9PXR5cGVvZiBlJiZcImRlZmF1bHRcImluIGU/ZTp7ZGVmYXVsdDplfX12YXIgdD1fKGUpLG49e25hbWU6XCJsYlwiLHdlZWtkYXlzOlwiU29ubmRlZ19Nw6lpbmRlZ19Ew6tuc2NoZGVnX03Dq3R0d29jaF9Eb25uZXNjaGRlZ19GcmVpZGVnX1NhbXNjaGRlZ1wiLnNwbGl0KFwiX1wiKSxtb250aHM6XCJKYW51YXJfRmVicnVhcl9Nw6RlcnpfQWJyw6tsbF9NZWVfSnVuaV9KdWxpX0F1Z3VzdF9TZXB0ZW1iZXJfT2t0b2Jlcl9Ob3ZlbWJlcl9EZXplbWJlclwiLnNwbGl0KFwiX1wiKSx3ZWVrU3RhcnQ6MSx3ZWVrZGF5c1Nob3J0OlwiU28uX03DqS5fRMOrLl9Nw6suX0RvLl9Gci5fU2EuXCIuc3BsaXQoXCJfXCIpLG1vbnRoc1Nob3J0OlwiSmFuLl9GZWJyLl9NcnouX0Fici5fTWVlX0p1bi5fSnVsLl9BdWcuX1NlcHQuX09rdC5fTm92Ll9EZXouXCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzTWluOlwiU29fTcOpX0TDq19Nw6tfRG9fRnJfU2FcIi5zcGxpdChcIl9cIiksb3JkaW5hbDpmdW5jdGlvbihlKXtyZXR1cm4gZX0sZm9ybWF0czp7TFQ6XCJIOm1tIFtBdWVyXVwiLExUUzpcIkg6bW06c3MgW0F1ZXJdXCIsTDpcIkRELk1NLllZWVlcIixMTDpcIkQuIE1NTU0gWVlZWVwiLExMTDpcIkQuIE1NTU0gWVlZWSBIOm1tIFtBdWVyXVwiLExMTEw6XCJkZGRkLCBELiBNTU1NIFlZWVkgSDptbSBbQXVlcl1cIn19O3JldHVybiB0LmRlZmF1bHQubG9jYWxlKG4sbnVsbCwhMCksbn0pKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/lb.js\n"));

/***/ })

}]);