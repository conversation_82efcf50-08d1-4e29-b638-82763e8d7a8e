import {
  <PERSON>,
  LinkField,
  Placeholder,
  RichTextField,
  withDatasourceCheck,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { useMediaQuery } from '@mantine/hooks';
import { RichText, useSitecoreContext } from '@sitecore-jss/sitecore-jss-react';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import Button from 'components/Elements/Button/Button';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Checkbox, Loader } from '@mantine/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleMinus, faEdit } from '@fortawesome/pro-regular-svg-icons';
import { UseFormReturnType, useForm } from '@mantine/form';
import { BankList, CardList, GetPaymentMethodsResponse } from 'src/services/MyAccountAPI/types';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios, { AxiosResponse } from 'axios-1.4';
import {
  autoPaySwapRequest,
  autoPaySwapResponse,
  DeleteAutoPayRequest,
  DeleteAutoPayResponse,
  getAutoPayEligibilityResponse,
  SetUpAutoPayEnrollBankRequest,
  SetUpAutoPayEnrollBankResponse,
  SetUpAutoPayEnrollCardRequest,
  SetUpAutoPayEnrollCardResponse,
} from 'src/services/PaymentAPI/types';
import { useAppSelector } from 'src/stores/store';
import { useDisclosure } from '@mantine/hooks';
import { useLoader, usePaymentAddedModal } from 'src/hooks/modalhooks';
import ConfirmationModal from 'components/common/ConfirmationModal/ConfirmationModal';
import { BillPayCombinedResponse } from 'src/services/BillPayCombinedAPI/types';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';
import Pen from 'assets/icons/Pen';
import { useRouter } from 'next/router';

type AutopayProps = ComponentProps & {
  fields: {
    data: {
      item: {
        PaymentMethodsTitleText: Field<string>;
        SaveChangesBtnText: Field<string>;
        CancelChangesBtnText: Field<string>;
        AutoPaySubText: Field<string>;
        AutoPayStatusText: Field<string>;
        AutoPayTurnOffText: Field<string>;
        AutoPayTurnOnText: Field<string>;
        AutoPayTurnOffBtnText: Field<string>;
        AutoPayTurnOnBtnText: Field<string>;
        ManagePaymentMethodsSubHeaderText: Field<string>;
        TermsAndConditionText: Field<string>;
        IAcceptText: Field<string>;
        AutoPayNotEligibleMessage: Field<string>;
        DefaultErrorMessage: Field<string>;
        AcceptErrorMessage: Field<string>;
        SelectPaymentMethodErrorMessage: Field<string>;
        PaymentMethodText: Field<string>;
        AccountEndingText: Field<string>;
        CardEndingText: Field<string>;
        EditText: Field<string>;
        AutoPayTermsAndConditionLink?: { jsonValue: LinkField };
        AutoPayTurnOnPastDueWarningMessage: RichTextField;
        AutoPayTurnOffPastDueWarningMessage: RichTextField;
      };
    };
  };
  openAddCardModal: () => void;
  openAddBankModal: () => void;
  isAutoPayTurnedOn: string;
  isPaymentAutopayModify?: boolean;
  updateAutopayModify?: (x: boolean) => void;
};

type AutoPayTurnedOnProps = {
  setExistingPaymentDetails(recurringPayments: CardList | BankList): unknown;
  AutoPayStatusText: string;
  AutoPayTurnOnText: string;
  AutoPayTurnOffBtnText: string;
  callPaymentMethods: boolean;
  setCallPaymentMethods: Dispatch<SetStateAction<boolean>>;
  setIsAutoPayOn: Dispatch<SetStateAction<string>>;
  selectedAccount: string;
  selectedAccountBPNumber: string;
  PaymentMethodText: string;
  AccountEndingText: string;
  CardEndingText: string;
  EditText: string;
  setIAgreeChecked: Dispatch<SetStateAction<boolean>>;
  AutoPayTurnOnPastDueWarningMessage: string | undefined;
  AutoPayTurnOffPastDueWarningMessage: string | undefined;
  SaveChangesBtnText: Field<string>;
  CancelChangesBtnText: Field<string>;
  AccountBalance: string | undefined;
  form: UseFormReturnType<{
    paymentMethod: string;
    paymentByCard: string;
  }>;
};

const AutoPayTurnOnView = (props: AutoPayTurnedOnProps) => {
  const selectedAccount = useAppSelector(
    (state) => state.authuser.accountSelection.contractAccount?.value
  );

  const [opened, { close, open }] = useDisclosure(false);
  let recurringPayments: CardList | BankList = {
    DefaultPaymentMethod: false,
    Expiration: '',
    BillingPostalCode: '',
    CardType: 0,
    PayAgent: '',
    PayChannel: '',
    HasRecurringPayments: false,
    HasScheduledPayments: false,
    RecurringContractAccountNumber: [],
    HoldersName: '',
    Nickname: '',
    Description: '',
    AccountId: '',
    DisplayAccountNumber: '',
    profileId: '',
    paymentType: 0,
    CardBrand: '',
  };

  let setDefaultValue: CardList | BankList;
  const { openModal, closeAllModal } = useLoader();

  // const isMobile = useMediaQuery('(max-width: 768px)');

  const handleSave = () => {
    if (props.AccountBalance && parseInt(props.AccountBalance) > 0) open();
    else deleteAutoPayMutation.mutate();
  };

  const confirmTurnOffAutoPay = () => {
    deleteAutoPayMutation.mutate();
    close();
  };

  const deleteAutoPayMutation = useMutation({
    mutationFn: () => {
      return axios.post<
        DeleteAutoPayResponse,
        AxiosResponse<DeleteAutoPayResponse, DeleteAutoPayRequest>,
        DeleteAutoPayRequest
      >('/api/autopay/deleteAutoPay', {
        Partner: props.selectedAccountBPNumber,
        ContractAccount: props.selectedAccount,
      });
    },
    onSuccess: (data) => {
      if (data.data.result.indicator === 'Success') {
        props.setCallPaymentMethods(false);
        props.setIsAutoPayOn('N');
        props.setExistingPaymentDetails(setDefaultValue);
      }
    },
    onMutate: () => {
      openModal();
    },
    onSettled: () => {
      closeAllModal();
    },
  });

  const { isLoading, data, error } = useQuery({
    queryKey: ['paymentmethods', selectedAccount],
    queryFn: () =>
      axios
        .get<GetPaymentMethodsResponse>('/api/myaccount/payments/paymentmethods', {
          params: {
            accountNumber: selectedAccount,
          },
        })
        .then((res) => res.data),
  });

  if (isLoading) return <></>;
  if (error) return <></>;

  data?.result.cardList.map((card) => {
    if (card.HasRecurringPayments) recurringPayments = card;
  });

  data?.result.bankList.map((bank) => {
    if (bank.HasRecurringPayments) recurringPayments = bank;
  });

  return (
    <>
      <div className="pt-5 text-base text-textQuattuordenary text-left">
        <span className="font-primaryBold text-textUndenary">{props.AutoPayStatusText}</span>
        <span className="font-primaryRegular text-textUndenary">{props.AutoPayTurnOnText}</span>
      </div>
      <div className="pt-5 text-base text-textQuattuordenary text-left">
        <span className="font-primaryBold text-textUndenary block mb-2">
          {props.PaymentMethodText}{' '}
        </span>
        <span className="font-primaryRegular text-textUndenary inline">
          {props?.form?.values?.paymentByCard === 'Y'
            ? props?.CardEndingText?.replace(
                '{accountNumber}',
                recurringPayments?.DisplayAccountNumber
              )
            : props?.AccountEndingText?.replace(
                '{accountNumber}',
                recurringPayments?.DisplayAccountNumber
              )}
        </span>

        <button
          type="button"
          onClick={() => {
            props.setCallPaymentMethods(true),
              props.setIAgreeChecked(false),
              props.setExistingPaymentDetails(recurringPayments);
          }}
          className="ml-4 cursor-pointer hover:text-textSecondary sm:inline-flex items-center font-primaryBold text-textPrimary inline"
        >
          {props.EditText}{' '}
          <span className="pl-2">
            <Pen />
          </span>
        </button>
      </div>
      {!props.callPaymentMethods && (
        <div className="pt-8 pb-0 hidden">
          <Button
            type="submit"
            variant={'secondary'}
            onClick={() => handleSave()}
            className="m-auto sm:ml-0 w-full sm:w-auto text-textQuinary bg-buttonSecondary hover:bg-buttonPrimary"
          >
            {props.AutoPayTurnOffBtnText}
          </Button>
        </div>
      )}

      <ConfirmationModal
        opened={opened}
        isMobile={false}
        onSave={confirmTurnOffAutoPay}
        onClose={close}
        title={props.AutoPayTurnOffBtnText}
        body={props.AutoPayTurnOffPastDueWarningMessage}
        saveButtonText={props?.SaveChangesBtnText?.value}
        cancelButtonText={props?.CancelChangesBtnText?.value}
      />
    </>
  );
};

const Autopay = (props: AutopayProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  const router = useRouter();

  if (isPageEditing) return <PageBuilder componentName="Autopay" />;

  const isMobile = useMediaQuery('(max-width: 768px)');
  const [opened, { close, open }] = useDisclosure(false);
  const [isAutoPayEligible, setIsAutoPayEligible] = useState<boolean>();
  const [isAutoPayOn, setIsAutoPayOn] = useState<string>();
  const [paymentDetails, setPaymentDetails] = useState<BankList | CardList>();
  const [isAddCardModalOpen, { open: openAddCardModal, close: closeAddCardModal }] =
    useDisclosure(false);
  const [isAddBankModalOpen, { open: openAddBankModal, close: closeAddBankModal }] =
    useDisclosure(false);
  const [callPaymentMethods, setCallPaymentMethods] = useState(false);
  const selectedAccount = useAppSelector(
    (state) => state.authuser.accountSelection.contractAccount?.value
  );
  const selectedAccountBPNumber = useAppSelector((state) => state.authuser.bpNumber);
  const { openPaymentAddedModal } = usePaymentAddedModal();
  const [iAgreeChecked, setIAgreeChecked] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | string[]>('');
  const [existingPaymentDetails, setExistingPaymentDetails] = useState<BankList | CardList>();
  const { openModal, closeAllModal } = useLoader();
  const [focusQuery, setFocusQuery] = useState(() => {
    return new URLSearchParams(window.location.search).get('focus')?.toLowerCase();
  });

  const updateAutopay = () => {
    if (props.updateAutopayModify) props.updateAutopayModify(true);
  };

  const addCardSuccess = (
    paymentNickName: string,
    paymentMethod: string,
    endingText: string,
    title: string,
    message: string,
    buttonText: string
  ) => {
    closeAddCardModal();
    openPaymentAddedModal(
      paymentNickName,
      paymentMethod,
      true,
      endingText,
      title,
      message,
      buttonText
    );
  };

  const addBankSuccess = (
    paymentNickName: string,
    paymentMethod: string,
    endingText: string,
    title: string,
    message: string,
    buttonText: string
  ) => {
    closeAddBankModal();
    openPaymentAddedModal(
      paymentNickName,
      paymentMethod,
      false,
      endingText,
      title,
      message,
      buttonText
    );
  };

  const { isLoading: getAutoPayEligibilityLoading, data: getAutoPayEligibility } = useQuery({
    queryKey: ['getAutoPayEligibility', selectedAccountBPNumber],
    queryFn: () =>
      axios
        .get<getAutoPayEligibilityResponse>('/api/autopay/getAutoPayEligibility', {
          params: {
            partnerNumber: selectedAccountBPNumber,
          },
        })
        .then((res) => res.data),
    enabled: !!selectedAccountBPNumber,
  });

  const {
    isLoading: getAccountBalanceLoading,
    data: getAccountBalance,
    refetch,
  } = useQuery({
    queryKey: ['accountbalance', selectedAccount],
    queryFn: () =>
      axios
        .get<BillPayCombinedResponse>('/api/billpaycombined', {
          params: {
            accountNumber: selectedAccount,
          },
        })
        .then((res) => res.data),
    enabled: !!selectedAccount,
  });

  //TODO - find a better approach to render autopay component
  useEffect(() => {
    getAutoPayEligibility && getAutoPayEligibility?.result?.eligibilityIndicator === 'Y'
      ? setIsAutoPayEligible(true)
      : setIsAutoPayEligible(false);
    setIsAutoPayOn(props.isAutoPayTurnedOn);
    setErrorMsg('');
  }, [getAutoPayEligibility, props.isAutoPayTurnedOn]);

  useEffect(() => {
    setCallPaymentMethods(false);
  }, [selectedAccount]);

  //When AutoPay Turned On
  const form = useForm({
    initialValues: {
      paymentMethod: '',
      paymentByCard: '',
    },
  });

  const setUpAutoPayEnrollCardmutation = useMutation({
    mutationFn: (values: CardList) => {
      return axios.post<
        SetUpAutoPayEnrollCardResponse,
        AxiosResponse<SetUpAutoPayEnrollCardResponse, SetUpAutoPayEnrollCardRequest>,
        SetUpAutoPayEnrollCardRequest
      >('/api/autopay/SetUpAutoPayEnrollCard', {
        IsNew: false,
        PartnerNumber: selectedAccountBPNumber,
        ContractAccount: selectedAccount,
        ExpiryDate: values.Expiration,
        CVV: '',
        CardNumber: values.AccountId,
        CardId: values.profileId,
        CardHolder: values.HoldersName,
        CardDescription: values.Description,
      });
    },
    onSuccess: (data) => {
      if (data.data.result.indicator === 'Success') {
        setCallPaymentMethods(false);
        setIsAutoPayOn('Y');
        updateAutopay();
      } else {
        setErrorMsg(props.fields.data?.item?.DefaultErrorMessage.value);
      }
      refetch();
    },
    onError: () => {
      setErrorMsg(props.fields.data?.item?.DefaultErrorMessage.value);
    },
    onMutate: () => {
      openModal();
    },
    onSettled: () => {
      closeAllModal();
    },
  });

  const setUpAutoPayEnrollBankmutation = useMutation({
    mutationFn: (values: BankList) => {
      return axios.post<
        SetUpAutoPayEnrollBankResponse,
        AxiosResponse<SetUpAutoPayEnrollBankResponse, SetUpAutoPayEnrollBankRequest>,
        SetUpAutoPayEnrollBankRequest
      >('/api/autopay/SetUpAutoPayEnrollBank', {
        IsNew: false,
        PartnerNumber: selectedAccountBPNumber,
        ContractAccount: selectedAccount,
        BankNumber: values.AccountId,
        BankRoutingNo: values.RoutingNumber,
        BankName: values.Nickname,
        BankAccountHolder: values.HoldersName,
        BankCountryKey: values.BankCountryKey,
        BankId: values.profileId,
      });
    },
    onSuccess: (data) => {
      if (data.data.result.indicator === 'Success') {
        setCallPaymentMethods(false);
        setIsAutoPayOn('Y');
        updateAutopay();
      } else {
        setErrorMsg(props.fields.data?.item?.DefaultErrorMessage.value);
      }
      refetch();
    },
    onError: () => {
      setErrorMsg(props.fields.data?.item?.DefaultErrorMessage.value);
    },
    onMutate: () => {
      openModal();
    },
    onSettled: () => {
      closeAllModal();
    },
  });

  const autoPaySwapEnroll = useMutation({
    mutationFn: (values: autoPaySwapRequest) => {
      return axios.post<
        autoPaySwapResponse,
        AxiosResponse<autoPaySwapResponse, autoPaySwapRequest>,
        autoPaySwapRequest
      >('/api/autopay/autoPaySwap', {
        isNew: values.isNew,
        partnerNumber: values.partnerNumber,
        contractAccount: values.contractAccount,
        expiryDate: '0001-01-01T00:00:00',
        cvv: values.cvv,
        cardToken: values.cardToken,
        bankToken: values.bankToken,
        bankRoutingNo: values.bankRoutingNo,
        oldCardId: values.oldCardId,
        newCardId: values.newCardId,
        oldBankId: values.oldBankId,
        newBankId: values.newBankId,
        cardPaymentAmount: values.cardPaymentAmount,
        cardHolder: values.cardHolder,
        bankAccountHolder: values.bankAccountHolder,
        bankName: values.bankName,
        bankCountryKey: values.bankCountryKey,
        cardDescription: values.cardDescription,
        paymentType: values.paymentType,
      });
    },
    onSuccess: (data) => {
      if (data.data.result.indicator === 'Success') {
        setCallPaymentMethods(false);
        setIsAutoPayOn('Y');
        updateAutopay();
      } else {
        setErrorMsg(props.fields.data?.item?.DefaultErrorMessage.value);
      }
      refetch();
    },
    onError: () => {
      setErrorMsg(props.fields.data?.item?.DefaultErrorMessage.value);
    },
    onMutate: () => {
      openModal();
    },
    onSettled: () => {
      closeAllModal();
    },
  });

  const handleSave = () => {
    if (iAgreeChecked) {
      setErrorMsg('');
      if (
        getAccountBalance?.result?.amount &&
        parseInt(getAccountBalance?.result?.amount) > 0 &&
        props.isAutoPayTurnedOn != 'Y'
      )
        open();
      else {
        saveChanges();
      }
    } else {
      setErrorMsg(props.fields.data?.item?.AcceptErrorMessage.value);
    }
  };

  const confirmTurnOffAutoPay = () => {
    saveChanges();
    close();
  };

  const saveChanges = async () => {
    setErrorMsg('');
    if (iAgreeChecked) {
      if (
        //card to card
        existingPaymentDetails &&
        existingPaymentDetails?.paymentType === 0 &&
        form.values.paymentByCard === 'Y'
      ) {
        const paymentCardDetails = paymentDetails as CardList;
        const autoPaySwapRequestValues = {
          isNew: false,
          partnerNumber: selectedAccountBPNumber,
          contractAccount: selectedAccount,
          expiryDate: new Date().toString(),
          cvv: '',
          cardToken: existingPaymentDetails?.AccountId,
          bankToken: '',
          bankRoutingNo: '',
          oldCardId: existingPaymentDetails?.profileId,
          newCardId: paymentCardDetails?.profileId,
          oldBankId: '',
          newBankId: '',
          cardPaymentAmount: 0.0,
          cardHolder: paymentCardDetails?.HoldersName,
          bankAccountHolder: '',
          bankName: '',
          bankCountryKey: '',
          cardDescription: paymentCardDetails?.Description,
          paymentType: 'Card',
        };
        autoPaySwapEnroll.mutate(autoPaySwapRequestValues as autoPaySwapRequest);
      } else if (
        //card to bank
        existingPaymentDetails &&
        existingPaymentDetails.paymentType === 0 &&
        form.values.paymentByCard === 'N'
      ) {
        const paymentBankDetails = paymentDetails as BankList;
        const autoPaySwapRequestValues = {
          isNew: false,
          partnerNumber: selectedAccountBPNumber,
          contractAccount: selectedAccount,
          expiryDate: new Date().toString(),
          cvv: '',
          cardToken: '',
          bankToken: paymentBankDetails?.AccountId,
          bankRoutingNo: paymentBankDetails?.RoutingNumber,
          oldCardId: existingPaymentDetails?.profileId,
          newCardId: '',
          oldBankId: '',
          newBankId: paymentDetails?.profileId,
          cardPaymentAmount: 0.0,
          cardHolder: '',
          bankAccountHolder: paymentDetails?.HoldersName,
          bankName: '',
          bankCountryKey: paymentBankDetails.BankCountryKey,
          cardDescription: '',
          paymentType: 'Bank',
        };
        autoPaySwapEnroll.mutate(autoPaySwapRequestValues as autoPaySwapRequest);
      } else if (
        //bank to bank
        existingPaymentDetails != undefined &&
        existingPaymentDetails.paymentType === 1 &&
        form.values.paymentByCard === 'N'
      ) {
        const paymentBankDetails = paymentDetails as BankList;
        const autoPaySwapRequestValues = {
          isNew: false,
          partnerNumber: selectedAccountBPNumber,
          contractAccount: selectedAccount,
          expiryDate: new Date().toString(),
          cvv: '',
          cardToken: '',
          bankToken: paymentBankDetails?.AccountId,
          bankRoutingNo: paymentBankDetails?.RoutingNumber,
          oldCardId: '',
          newCardId: '',
          oldBankId: existingPaymentDetails.profileId,
          newBankId: paymentBankDetails?.profileId,
          cardPaymentAmount: 0.0,
          cardHolder: '',
          bankAccountHolder: paymentBankDetails?.HoldersName,
          bankName: '',
          bankCountryKey: paymentBankDetails.BankCountryKey,
          cardDescription: '',
          paymentType: 'Bank',
        };
        autoPaySwapEnroll.mutate(autoPaySwapRequestValues as autoPaySwapRequest);
      } else if (
        //bank to card
        existingPaymentDetails != undefined &&
        existingPaymentDetails.paymentType === 1 &&
        form.values.paymentByCard === 'Y'
      ) {
        const paymentCardDetails = paymentDetails as CardList;
        const autoPaySwapRequestValues = {
          isNew: false,
          partnerNumber: selectedAccountBPNumber,
          contractAccount: selectedAccount,
          expiryDate: new Date().toString(),
          cvv: '',
          cardToken: paymentCardDetails?.AccountId,
          bankToken: '',
          bankRoutingNo: '',
          oldCardId: '',
          newCardId: paymentCardDetails.profileId,
          oldBankId: existingPaymentDetails?.profileId,
          newBankId: '',
          cardPaymentAmount: 0.0,
          cardHolder: paymentCardDetails.HoldersName,
          bankAccountHolder: '',
          bankName: '',
          bankCountryKey: '',
          cardDescription: paymentCardDetails.Description,
          paymentType: 'Card',
        };
        autoPaySwapEnroll.mutate(autoPaySwapRequestValues as autoPaySwapRequest);
      } else if (form.values.paymentByCard === 'Y') {
        setUpAutoPayEnrollCardmutation.mutate(paymentDetails as CardList);
      } else if (form.values.paymentByCard === 'N') {
        setUpAutoPayEnrollBankmutation.mutate(paymentDetails as BankList);
      } else {
        setErrorMsg(props.fields.data?.item?.SelectPaymentMethodErrorMessage.value);
      }
    } else {
      setErrorMsg(props.fields.data?.item?.AcceptErrorMessage.value);
    }
  };

  if (getAutoPayEligibilityLoading || getAccountBalanceLoading) {
    return (
      <>
        <div className="w-full md:w-1/3 lg:m-1/4 sm:p-0 sm:flex-1">
          <div className="flex justify-center items-center min-h-[200px] p-5">
            <Loader color="blue" />
          </div>
        </div>
      </>
    );
  } else if (isAutoPayEligible) {
    return (
      <>
        <div className="text-center sm:text-left">
          <div className="sm:text-plus2 font-primaryBold text-textUndenary text-plus1 text-left">
            <h3>{props.fields.data?.item?.PaymentMethodsTitleText.value}</h3>
          </div>
          <div className={`${callPaymentMethods ? 'pt-5 mb-8' : 'pt-5'}`}>
            <RichText
              tag="p"
              className="text-minus1 sm:text-base w-full tracking-normal font-primaryRegular text-textUndenary text-left max-w-[636px]"
              field={{
                value: props.fields.data?.item?.AutoPaySubText.value,
              }}
            ></RichText>
          </div>
          {/* autopay turned on view*/}
          {isAutoPayOn === 'Y' && !callPaymentMethods && (
            <AutoPayTurnOnView
              AutoPayStatusText={props.fields.data?.item?.AutoPayStatusText.value}
              AutoPayTurnOnText={props.fields.data?.item?.AutoPayTurnOnText.value}
              AutoPayTurnOffBtnText={props.fields.data?.item?.AutoPayTurnOffBtnText.value}
              callPaymentMethods={callPaymentMethods}
              setCallPaymentMethods={setCallPaymentMethods}
              setIsAutoPayOn={setIsAutoPayOn}
              selectedAccount={selectedAccount}
              selectedAccountBPNumber={selectedAccountBPNumber}
              PaymentMethodText={props.fields.data?.item?.PaymentMethodText.value}
              AccountEndingText={props.fields.data?.item?.AccountEndingText.value}
              CardEndingText={props.fields.data?.item?.CardEndingText.value}
              EditText={props.fields.data?.item?.EditText.value}
              setIAgreeChecked={setIAgreeChecked}
              form={form}
              setExistingPaymentDetails={setExistingPaymentDetails}
              AutoPayTurnOnPastDueWarningMessage={
                props.fields.data?.item?.AutoPayTurnOnPastDueWarningMessage.value
              }
              AutoPayTurnOffPastDueWarningMessage={
                props.fields.data?.item?.AutoPayTurnOffPastDueWarningMessage.value
              }
              AccountBalance={getAccountBalance?.result?.amount}
              SaveChangesBtnText={props?.fields?.data?.item?.SaveChangesBtnText}
              CancelChangesBtnText={props?.fields?.data?.item?.CancelChangesBtnText}
            />
          )}
          {/* autopay turned off view*/}
          {((isAutoPayOn === 'N' && !callPaymentMethods) || focusQuery === 'autopay') && (
            <>
              <div className="pt-5 text-base text-textQuattuordenary">
                <span className="font-primaryBold">
                  {focusQuery !== 'autopay' && props.fields.data?.item?.AutoPayStatusText.value}
                </span>
                <span className="font-primaryRegular">
                  {focusQuery !== 'autopay' && props.fields.data?.item?.AutoPayTurnOffText.value}
                </span>
              </div>
              <div className="pt-8 pb-0">
                {focusQuery !== 'autopay' && (
                  <Button
                    type="submit"
                    variant={isMobile ? 'secondary' : 'primary'}
                    onClick={() => {
                      setCallPaymentMethods(true), setIAgreeChecked(false);
                    }}
                    className="m-auto sm:ml-0 ml-0 mr-0 w-full sm:w-auto text-textQuinary bg-buttonSecondary hover:bg-buttonPrimary"
                  >
                    {props.fields.data?.item?.AutoPayTurnOnBtnText.value}
                  </Button>
                )}
              </div>
            </>
          )}
          {(callPaymentMethods || focusQuery === 'autopay') && (
            <>
              <Placeholder
                name={'jss-bill-and-payment'}
                rendering={props.rendering}
                form={form}
                formField="paymentMethod"
                isAddCardModalOpen={isAddCardModalOpen}
                openAddCardModal={openAddCardModal}
                closeAddCardModal={closeAddCardModal}
                isAddBankModalOpen={isAddBankModalOpen}
                openAddBankModal={openAddBankModal}
                closeAddBankModal={closeAddBankModal}
                addCardSuccess={addCardSuccess}
                addBankSuccess={addBankSuccess}
                setPaymentDetails={setPaymentDetails}
                isAutoPayPaymentMethod={true}
                isAutoPayOn={isAutoPayOn}
                bankPaymentIndicator={
                  getAutoPayEligibility !== undefined &&
                  getAutoPayEligibility?.result?.bankPaymentIndicator
                }
                cardPaymentIndicator={
                  getAutoPayEligibility !== undefined &&
                  getAutoPayEligibility?.result?.cardPaymentIndicator
                }
              />
              <div className="">
                <Checkbox
                  label={
                    <>
                      <label>{props?.fields.data?.item?.IAcceptText?.value}</label>{' '}
                      <a
                        href={
                          props.fields.data?.item?.AutoPayTermsAndConditionLink?.jsonValue?.value
                            .href
                        }
                        rel="noreferrer"
                        target="_blank"
                      >
                        <span className="text-textPrimary hover:text-textSecondary sm:text-minus1 font-primaryBold">
                          {
                            props?.fields?.data?.item?.AutoPayTermsAndConditionLink?.jsonValue
                              ?.value.text
                          }
                        </span>
                      </a>
                    </>
                  }
                  styles={{
                    body: {
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '25px',
                      marginTop: '20px',
                    },
                  }}
                  checked={iAgreeChecked}
                  onChange={(event) => setIAgreeChecked(event.currentTarget.checked)}
                />
              </div>
              <div className="sm:flex items-center max-w-[590px] w-full mb-10 text-center">
                <div className="flex flex-col sm:flex-row gap-5 mb-5">
                  <Button
                    type="button"
                    className="w-full sm:w-auto mb-0 text-textQuinary"
                    onClick={() => handleSave()}
                  >
                    {props.fields.data?.item?.SaveChangesBtnText.value}
                  </Button>
                  <Button
                    type="button"
                    variant="secondary"
                    className="w-full sm:w-auto mb-0 flex"
                    onClick={() => {
                      // setIsAutoPayOn(props.isAutoPayTurnedOn),
                      setCallPaymentMethods(false),
                        setErrorMsg(''),
                        (form.values.paymentByCard = '');
                      const url = new URL(window.location.href);
                      url.searchParams.delete('focus');
                      window.history.replaceState({}, '', url);
                      setFocusQuery('');
                    }}
                  >
                    {props.fields.data?.item?.CancelChangesBtnText.value}
                  </Button>
                </div>
                <button
                  type="button"
                  className="hidden text-base ml-[30px] cursor-pointer text-textPrimary hover:text-textSecondary sm:text-minus1"
                  onClick={() => {
                    // setIsAutoPayOn(props.isAutoPayTurnedOn),
                    setCallPaymentMethods(false), setErrorMsg(''), (form.values.paymentByCard = '');
                  }}
                >
                  {props.fields.data?.item?.CancelChangesBtnText.value}
                  <FontAwesomeIcon icon={faCircleMinus} className="ml-[5px] hidden" />
                </button>
              </div>
            </>
          )}

          <RichText
            tag="p"
            className="font-primaryBold text-minus1 text-textDenary"
            field={{ value: Array.isArray(errorMsg) ? errorMsg.join(' ') : errorMsg || '' }}
          />
        </div>

        <ConfirmationModal
          opened={opened}
          isMobile={false}
          onSave={confirmTurnOffAutoPay}
          onClose={close}
          title={props.fields.data?.item?.AutoPayTurnOnBtnText.value}
          body={props.fields.data?.item?.AutoPayTurnOnPastDueWarningMessage.value
            ?.toString()
            .replace('{AccountBalance}', ('$' + getAccountBalance?.result.amount) as string)}
          saveButtonText={props?.fields?.data?.item?.SaveChangesBtnText?.value}
          cancelButtonText={props?.fields?.data?.item?.CancelChangesBtnText?.value}
        />
      </>
    );
  } else {
    return (
      <div>
        <div className="font-primaryBlack">
          <span className="text-base sm:text-plus2 text-textPrimary">
            {props.fields.data?.item?.PaymentMethodsTitleText.value}
          </span>
        </div>
        <div className={`${callPaymentMethods ? 'pt-5 mb-8' : 'pt-5'}`}>
          <RichText
            tag="p"
            className="font-primaryRegular text-textPrimary text-minus1 sm:text-base -tracking-[0.25px]"
            field={{
              value: props.fields.data?.item?.AutoPayNotEligibleMessage.value,
            }}
          ></RichText>
        </div>
      </div>
    );
  }
};

export { Autopay };
const Component = withDatasourceCheck()<AutopayProps>(Autopay);
export default aiLogger(Component, Component.name);
