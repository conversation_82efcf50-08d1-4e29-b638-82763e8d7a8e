/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_tl-ph_js"],{

/***/ "./node_modules/dayjs/locale/tl-ph.js":
/*!********************************************!*\
  !*** ./node_modules/dayjs/locale/tl-ph.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,a){ true?module.exports=a(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),t={name:\"tl-ph\",weekdays:\"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado\".split(\"_\"),months:\"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre\".split(\"_\"),weekStart:1,weekdaysShort:\"Lin_Lun_Mar_Miy_Huw_Biy_Sab\".split(\"_\"),monthsShort:\"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis\".split(\"_\"),weekdaysMin:\"Li_Lu_Ma_Mi_Hu_Bi_Sab\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"MM/D/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY HH:mm\",LLLL:\"dddd, MMMM DD, YYYY HH:mm\"},relativeTime:{future:\"sa loob ng %s\",past:\"%s ang nakalipas\",s:\"ilang segundo\",m:\"isang minuto\",mm:\"%d minuto\",h:\"isang oras\",hh:\"%d oras\",d:\"isang araw\",dd:\"%d araw\",M:\"isang buwan\",MM:\"%d buwan\",y:\"isang taon\",yy:\"%d taon\"}};return n.default.locale(t,null,!0),t}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/tl-ph.js\n"));

/***/ })

}]);