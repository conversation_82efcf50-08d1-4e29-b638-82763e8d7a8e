/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ga_js"],{

/***/ "./node_modules/dayjs/locale/ga.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ga.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(a,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var i=e(a),n={name:\"ga\",weekdays:\"Dé Domhnaigh_Dé Luain_Dé Máirt_Dé Céadaoin_Déardaoin_Dé hAoine_Dé Satharn\".split(\"_\"),months:\"Eanáir_Feabhra_Márta_Aibreán_Bealtaine_Méitheamh_Iúil_Lúnasa_Meán Fómhair_Deaireadh Fómhair_Samhain_Nollaig\".split(\"_\"),weekStart:1,weekdaysShort:\"Dom_Lua_Mái_Céa_Déa_hAo_Sat\".split(\"_\"),monthsShort:\"Eaná_Feab_Márt_Aibr_Beal_Méit_Iúil_Lúna_Meán_Deai_Samh_Noll\".split(\"_\"),weekdaysMin:\"Do_Lu_Má_Ce_Dé_hA_Sa\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"i %s\",past:\"%s ó shin\",s:\"cúpla soicind\",m:\"nóiméad\",mm:\"%d nóiméad\",h:\"uair an chloig\",hh:\"%d uair an chloig\",d:\"lá\",dd:\"%d lá\",M:\"mí\",MM:\"%d mí\",y:\"bliain\",yy:\"%d bliain\"}};return i.default.locale(n,null,!0),n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ga.js\n"));

/***/ })

}]);