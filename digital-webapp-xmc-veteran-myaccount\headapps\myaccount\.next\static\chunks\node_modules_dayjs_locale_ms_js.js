/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ms_js"],{

/***/ "./node_modules/dayjs/locale/ms.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ms.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,a){ true?module.exports=a(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=a(e),s={name:\"ms\",weekdays:\"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu\".split(\"_\"),weekdaysShort:\"Ahd_Isn_Sel_Rab_Kha_Jum_Sab\".split(\"_\"),weekdaysMin:\"Ah_Is_Sl_Rb_Km_Jm_Sb\".split(\"_\"),months:\"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember\".split(\"_\"),monthsShort:\"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis\".split(\"_\"),weekStart:1,formats:{LT:\"HH.mm\",LTS:\"HH.mm.ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH.mm\",LLLL:\"dddd, D MMMM YYYY HH.mm\"},relativeTime:{future:\"dalam %s\",past:\"%s yang lepas\",s:\"beberapa saat\",m:\"seminit\",mm:\"%d minit\",h:\"sejam\",hh:\"%d jam\",d:\"sehari\",dd:\"%d hari\",M:\"sebulan\",MM:\"%d bulan\",y:\"setahun\",yy:\"%d tahun\"},ordinal:function(e){return e+\".\"}};return t.default.locale(s,null,!0),s}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ms.js\n"));

/***/ })

}]);