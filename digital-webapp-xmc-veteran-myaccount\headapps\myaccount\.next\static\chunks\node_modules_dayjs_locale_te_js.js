/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_te_js"],{

/***/ "./node_modules/dayjs/locale/te.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/te.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"te\",weekdays:\"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం\".split(\"_\"),months:\"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్\".split(\"_\"),weekdaysShort:\"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని\".split(\"_\"),monthsShort:\"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.\".split(\"_\"),weekdaysMin:\"ఆ_సో_మం_బు_గు_శు_శ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm\",LTS:\"A h:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm\",LLLL:\"dddd, D MMMM YYYY, A h:mm\"},relativeTime:{future:\"%s లో\",past:\"%s క్రితం\",s:\"కొన్ని క్షణాలు\",m:\"ఒక నిమిషం\",mm:\"%d నిమిషాలు\",h:\"ఒక గంట\",hh:\"%d గంటలు\",d:\"ఒక రోజు\",dd:\"%d రోజులు\",M:\"ఒక నెల\",MM:\"%d నెలలు\",y:\"ఒక సంవత్సరం\",yy:\"%d సంవత్సరాలు\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/te.js\n"));

/***/ })

}]);