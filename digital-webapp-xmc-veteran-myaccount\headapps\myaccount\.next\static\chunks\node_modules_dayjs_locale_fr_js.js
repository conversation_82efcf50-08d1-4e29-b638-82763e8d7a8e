/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_fr_js"],{

/***/ "./node_modules/dayjs/locale/fr.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/fr.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,n){ true?module.exports=n(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),i={name:\"fr\",weekdays:\"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi\".split(\"_\"),weekdaysShort:\"dim._lun._mar._mer._jeu._ven._sam.\".split(\"_\"),weekdaysMin:\"di_lu_ma_me_je_ve_sa\".split(\"_\"),months:\"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre\".split(\"_\"),monthsShort:\"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.\".split(\"_\"),weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"dans %s\",past:\"il y a %s\",s:\"quelques secondes\",m:\"une minute\",mm:\"%d minutes\",h:\"une heure\",hh:\"%d heures\",d:\"un jour\",dd:\"%d jours\",M:\"un mois\",MM:\"%d mois\",y:\"un an\",yy:\"%d ans\"},ordinal:function(e){return\"\"+e+(1===e?\"er\":\"\")}};return t.default.locale(i,null,!0),i}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/fr.js\n"));

/***/ })

}]);