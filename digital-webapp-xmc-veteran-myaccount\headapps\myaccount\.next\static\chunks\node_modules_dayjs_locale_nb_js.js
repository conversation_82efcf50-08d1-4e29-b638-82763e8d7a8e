/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_nb_js"],{

/***/ "./node_modules/dayjs/locale/nb.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/nb.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=t(e),a={name:\"nb\",weekdays:\"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag\".split(\"_\"),weekdaysShort:\"sø._ma._ti._on._to._fr._lø.\".split(\"_\"),weekdaysMin:\"sø_ma_ti_on_to_fr_lø\".split(\"_\"),months:\"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember\".split(\"_\"),monthsShort:\"jan._feb._mars_april_mai_juni_juli_aug._sep._okt._nov._des.\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY [kl.] HH:mm\",LLLL:\"dddd D. MMMM YYYY [kl.] HH:mm\"},relativeTime:{future:\"om %s\",past:\"%s siden\",s:\"noen sekunder\",m:\"ett minutt\",mm:\"%d minutter\",h:\"en time\",hh:\"%d timer\",d:\"en dag\",dd:\"%d dager\",M:\"en måned\",MM:\"%d måneder\",y:\"ett år\",yy:\"%d år\"}};return n.default.locale(a,null,!0),a}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/nb.js\n"));

/***/ })

}]);