/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_ca_js"],{

/***/ "./node_modules/dayjs/locale/ca.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/ca.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,s){ true?module.exports=s(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=s(e),_={name:\"ca\",weekdays:\"Diumenge_Dilluns_Dimarts_Dimecres_Dijous_Divendres_Dissabte\".split(\"_\"),weekdaysShort:\"Dg._Dl._Dt._Dc._Dj._Dv._Ds.\".split(\"_\"),weekdaysMin:\"Dg_Dl_Dt_Dc_Dj_Dv_Ds\".split(\"_\"),months:\"Gener_Febrer_Març_Abril_Maig_Juny_Juliol_Agost_Setembre_Octubre_Novembre_Desembre\".split(\"_\"),monthsShort:\"Gen._Febr._Març_Abr._Maig_Juny_Jul._Ag._Set._Oct._Nov._Des.\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM [de] YYYY\",LLL:\"D MMMM [de] YYYY [a les] H:mm\",LLLL:\"dddd D MMMM [de] YYYY [a les] H:mm\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY, H:mm\",llll:\"ddd D MMM YYYY, H:mm\"},relativeTime:{future:\"d'aquí %s\",past:\"fa %s\",s:\"uns segons\",m:\"un minut\",mm:\"%d minuts\",h:\"una hora\",hh:\"%d hores\",d:\"un dia\",dd:\"%d dies\",M:\"un mes\",MM:\"%d mesos\",y:\"un any\",yy:\"%d anys\"},ordinal:function(e){return\"\"+e+(1===e||3===e?\"r\":2===e?\"n\":4===e?\"t\":\"è\")}};return t.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/ca.js\n"));

/***/ })

}]);