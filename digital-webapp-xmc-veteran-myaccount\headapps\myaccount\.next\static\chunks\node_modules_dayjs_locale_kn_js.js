/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_kn_js"],{

/***/ "./node_modules/dayjs/locale/kn.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/kn.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"kn\",weekdays:\"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ\".split(\"_\"),months:\"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್\".split(\"_\"),weekdaysShort:\"ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ\".split(\"_\"),monthsShort:\"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ\".split(\"_\"),weekdaysMin:\"ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm\",LTS:\"A h:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm\",LLLL:\"dddd, D MMMM YYYY, A h:mm\"},relativeTime:{future:\"%s ನಂತರ\",past:\"%s ಹಿಂದೆ\",s:\"ಕೆಲವು ಕ್ಷಣಗಳು\",m:\"ಒಂದು ನಿಮಿಷ\",mm:\"%d ನಿಮಿಷ\",h:\"ಒಂದು ಗಂಟೆ\",hh:\"%d ಗಂಟೆ\",d:\"ಒಂದು ದಿನ\",dd:\"%d ದಿನ\",M:\"ಒಂದು ತಿಂಗಳು\",MM:\"%d ತಿಂಗಳು\",y:\"ಒಂದು ವರ್ಷ\",yy:\"%d ವರ್ಷ\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/kn.js\n"));

/***/ })

}]);