/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_bo_js"],{

/***/ "./node_modules/dayjs/locale/bo.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/bo.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"bo\",weekdays:\"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་\".split(\"_\"),weekdaysShort:\"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་\".split(\"_\"),weekdaysMin:\"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་\".split(\"_\"),months:\"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ\".split(\"_\"),monthsShort:\"ཟླ་དང་པོ_ཟླ་གཉིས་པ_ཟླ་གསུམ་པ_ཟླ་བཞི་པ_ཟླ་ལྔ་པ_ཟླ་དྲུག་པ_ཟླ་བདུན་པ_ཟླ་བརྒྱད་པ_ཟླ་དགུ་པ_ཟླ་བཅུ་པ_ཟླ་བཅུ་གཅིག་པ_ཟླ་བཅུ་གཉིས་པ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm\",LTS:\"A h:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm\",LLLL:\"dddd, D MMMM YYYY, A h:mm\"},relativeTime:{future:\"%s ལ་\",past:\"%s སྔོན་ལ་\",s:\"ཏོག་ཙམ་\",m:\"སྐར་མ་གཅིག་\",mm:\"སྐར་མ་ %d\",h:\"ཆུ་ཚོད་གཅིག་\",hh:\"ཆུ་ཚོད་ %d\",d:\"ཉིན་གཅིག་\",dd:\"ཉིན་ %d\",M:\"ཟླ་བ་གཅིག་\",MM:\"ཟླ་བ་ %d\",y:\"ལོ་གཅིག་\",yy:\"ལོ་ %d\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/bo.js\n"));

/***/ })

}]);