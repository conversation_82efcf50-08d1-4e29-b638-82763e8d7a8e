/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_lv_js"],{

/***/ "./node_modules/dayjs/locale/lv.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/lv.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,s){ true?module.exports=s(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=s(e),d={name:\"lv\",weekdays:\"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena\".split(\"_\"),months:\"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris\".split(\"_\"),weekStart:1,weekdaysShort:\"Sv_P_O_T_C_Pk_S\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec\".split(\"_\"),weekdaysMin:\"Sv_P_O_T_C_Pk_S\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY.\",LL:\"YYYY. [gada] D. MMMM\",LLL:\"YYYY. [gada] D. MMMM, HH:mm\",LLLL:\"YYYY. [gada] D. MMMM, dddd, HH:mm\"},relativeTime:{future:\"pēc %s\",past:\"pirms %s\",s:\"dažām sekundēm\",m:\"minūtes\",mm:\"%d minūtēm\",h:\"stundas\",hh:\"%d stundām\",d:\"dienas\",dd:\"%d dienām\",M:\"mēneša\",MM:\"%d mēnešiem\",y:\"gada\",yy:\"%d gadiem\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/lv.js\n"));

/***/ })

}]);