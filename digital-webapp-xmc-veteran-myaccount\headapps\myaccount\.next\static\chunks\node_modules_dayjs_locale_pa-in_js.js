/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_pa-in_js"],{

/***/ "./node_modules/dayjs/locale/pa-in.js":
/*!********************************************!*\
  !*** ./node_modules/dayjs/locale/pa-in.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"pa-in\",weekdays:\"ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ\".split(\"_\"),months:\"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ\".split(\"_\"),weekdaysShort:\"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ\".split(\"_\"),monthsShort:\"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ\".split(\"_\"),weekdaysMin:\"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm ਵਜੇ\",LTS:\"A h:mm:ss ਵਜੇ\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm ਵਜੇ\",LLLL:\"dddd, D MMMM YYYY, A h:mm ਵਜੇ\"},relativeTime:{future:\"%s ਵਿੱਚ\",past:\"%s ਪਿਛਲੇ\",s:\"ਕੁਝ ਸਕਿੰਟ\",m:\"ਇਕ ਮਿੰਟ\",mm:\"%d ਮਿੰਟ\",h:\"ਇੱਕ ਘੰਟਾ\",hh:\"%d ਘੰਟੇ\",d:\"ਇੱਕ ਦਿਨ\",dd:\"%d ਦਿਨ\",M:\"ਇੱਕ ਮਹੀਨਾ\",MM:\"%d ਮਹੀਨੇ\",y:\"ਇੱਕ ਸਾਲ\",yy:\"%d ਸਾਲ\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/pa-in.js\n"));

/***/ })

}]);