/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_gu_js"],{

/***/ "./node_modules/dayjs/locale/gu.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/gu.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(_,e){ true?module.exports=e(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"gu\",weekdays:\"રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર\".split(\"_\"),months:\"જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર\".split(\"_\"),weekdaysShort:\"રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ\".split(\"_\"),monthsShort:\"જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.\".split(\"_\"),weekdaysMin:\"ર_સો_મં_બુ_ગુ_શુ_શ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm વાગ્યે\",LTS:\"A h:mm:ss વાગ્યે\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm વાગ્યે\",LLLL:\"dddd, D MMMM YYYY, A h:mm વાગ્યે\"},relativeTime:{future:\"%s મા\",past:\"%s પેહલા\",s:\"અમુક પળો\",m:\"એક મિનિટ\",mm:\"%d મિનિટ\",h:\"એક કલાક\",hh:\"%d કલાક\",d:\"એક દિવસ\",dd:\"%d દિવસ\",M:\"એક મહિનો\",MM:\"%d મહિનો\",y:\"એક વર્ષ\",yy:\"%d વર્ષ\"}};return t.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/gu.js\n"));

/***/ })

}]);