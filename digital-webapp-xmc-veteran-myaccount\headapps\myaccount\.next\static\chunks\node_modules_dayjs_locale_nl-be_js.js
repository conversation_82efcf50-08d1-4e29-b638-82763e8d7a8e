/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_nl-be_js"],{

/***/ "./node_modules/dayjs/locale/nl-be.js":
/*!********************************************!*\
  !*** ./node_modules/dayjs/locale/nl-be.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,a){ true?module.exports=a(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),d={name:\"nl-be\",weekdays:\"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag\".split(\"_\"),months:\"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.\".split(\"_\"),weekStart:1,weekdaysShort:\"zo._ma._di._wo._do._vr._za.\".split(\"_\"),weekdaysMin:\"zo_ma_di_wo_do_vr_za\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"over %s\",past:\"%s geleden\",s:\"een paar seconden\",m:\"één minuut\",mm:\"%d minuten\",h:\"één uur\",hh:\"%d uur\",d:\"één dag\",dd:\"%d dagen\",M:\"één maand\",MM:\"%d maanden\",y:\"één jaar\",yy:\"%d jaar\"}};return n.default.locale(d,null,!0),d}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/nl-be.js\n"));

/***/ })

}]);