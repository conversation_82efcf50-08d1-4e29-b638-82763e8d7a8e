/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_tzl_js"],{

/***/ "./node_modules/dayjs/locale/tzl.js":
/*!******************************************!*\
  !*** ./node_modules/dayjs/locale/tzl.js ***!
  \******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,_){ true?module.exports=_(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),a={name:\"tzl\",weekdays:\"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi\".split(\"_\"),months:\"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar\".split(\"_\"),weekStart:1,weekdaysShort:\"Súl_Lún_Mai_Már_Xhú_Vié_Sát\".split(\"_\"),monthsShort:\"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec\".split(\"_\"),weekdaysMin:\"Sú_Lú_Ma_Má_Xh_Vi_Sá\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH.mm\",LTS:\"HH.mm.ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM [dallas] YYYY\",LLL:\"D. MMMM [dallas] YYYY HH.mm\",LLLL:\"dddd, [li] D. MMMM [dallas] YYYY HH.mm\"}};return t.default.locale(a,null,!0),a}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvbG9jYWxlL3R6bC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLEtBQW9ELGtCQUFrQixtQkFBTyxDQUFDLGdEQUFPLEdBQUcsQ0FBMkksQ0FBQyxtQkFBbUIsYUFBYSxjQUFjLCtDQUErQyxXQUFXLGNBQWMsMllBQTJZLFNBQVMsVUFBVSxzSkFBc0oscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9kYXlqcy9sb2NhbGUvdHpsLmpzPzA1NzYiXSwic291cmNlc0NvbnRlbnQiOlsiIWZ1bmN0aW9uKGUsXyl7XCJvYmplY3RcIj09dHlwZW9mIGV4cG9ydHMmJlwidW5kZWZpbmVkXCIhPXR5cGVvZiBtb2R1bGU/bW9kdWxlLmV4cG9ydHM9XyhyZXF1aXJlKFwiZGF5anNcIikpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUoW1wiZGF5anNcIl0sXyk6KGU9XCJ1bmRlZmluZWRcIiE9dHlwZW9mIGdsb2JhbFRoaXM/Z2xvYmFsVGhpczplfHxzZWxmKS5kYXlqc19sb2NhbGVfdHpsPV8oZS5kYXlqcyl9KHRoaXMsKGZ1bmN0aW9uKGUpe1widXNlIHN0cmljdFwiO2Z1bmN0aW9uIF8oZSl7cmV0dXJuIGUmJlwib2JqZWN0XCI9PXR5cGVvZiBlJiZcImRlZmF1bHRcImluIGU/ZTp7ZGVmYXVsdDplfX12YXIgdD1fKGUpLGE9e25hbWU6XCJ0emxcIix3ZWVrZGF5czpcIlPDumxhZGlfTMO6bmXDp2lfTWFpdHppX03DoXJjdXJpX1how7phZGlfVmnDqW5lcsOnaV9Tw6F0dXJpXCIuc3BsaXQoXCJfXCIpLG1vbnRoczpcIkphbnVhcl9GZXZyYWdsaF9NYXLDp19BdnLDr3VfTWFpX0fDvG5fSnVsaWFfR3VzY2h0X1NldGVtdmFyX0xpc3RvcMOkdHNfTm9lbXZhcl9aZWNlbXZhclwiLnNwbGl0KFwiX1wiKSx3ZWVrU3RhcnQ6MSx3ZWVrZGF5c1Nob3J0OlwiU8O6bF9Mw7puX01haV9Nw6FyX1how7pfVmnDqV9Tw6F0XCIuc3BsaXQoXCJfXCIpLG1vbnRoc1Nob3J0OlwiSmFuX0Zldl9NYXJfQXZyX01haV9Hw7xuX0p1bF9HdXNfU2V0X0xpc19Ob2VfWmVjXCIuc3BsaXQoXCJfXCIpLHdlZWtkYXlzTWluOlwiU8O6X0zDul9NYV9Nw6FfWGhfVmlfU8OhXCIuc3BsaXQoXCJfXCIpLG9yZGluYWw6ZnVuY3Rpb24oZSl7cmV0dXJuIGV9LGZvcm1hdHM6e0xUOlwiSEgubW1cIixMVFM6XCJISC5tbS5zc1wiLEw6XCJERC5NTS5ZWVlZXCIsTEw6XCJELiBNTU1NIFtkYWxsYXNdIFlZWVlcIixMTEw6XCJELiBNTU1NIFtkYWxsYXNdIFlZWVkgSEgubW1cIixMTExMOlwiZGRkZCwgW2xpXSBELiBNTU1NIFtkYWxsYXNdIFlZWVkgSEgubW1cIn19O3JldHVybiB0LmRlZmF1bHQubG9jYWxlKGEsbnVsbCwhMCksYX0pKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/tzl.js\n"));

/***/ })

}]);