/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_dayjs_locale_jv_js"],{

/***/ "./node_modules/dayjs/locale/jv.js":
/*!*****************************************!*\
  !*** ./node_modules/dayjs/locale/jv.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,n){ true?module.exports=n(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\")):0}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),_={name:\"jv\",weekdays:\"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu\".split(\"_\"),months:\"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember\".split(\"_\"),weekStart:1,weekdaysShort:\"Min_Sen_Sel_Reb_Kem_Jem_Sep\".split(\"_\"),monthsShort:\"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des\".split(\"_\"),weekdaysMin:\"Mg_Sn_Sl_Rb_Km_Jm_Sp\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH.mm\",LTS:\"HH.mm.ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY [pukul] HH.mm\",LLLL:\"dddd, D MMMM YYYY [pukul] HH.mm\"},relativeTime:{future:\"wonten ing %s\",past:\"%s ingkang kepengker\",s:\"sawetawis detik\",m:\"setunggal menit\",mm:\"%d menit\",h:\"setunggal jam\",hh:\"%d jam\",d:\"sedinten\",dd:\"%d dinten\",M:\"sewulan\",MM:\"%d wulan\",y:\"setaun\",yy:\"%d taun\"}};return t.default.locale(_,null,!0),_}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/locale/jv.js\n"));

/***/ })

}]);